{"editor.formatOnSave": true, "editor.defaultFormatter": "Dart-Code.dart-code", "[dart]": {"editor.formatOnSave": true, "editor.defaultFormatter": "Dart-Code.dart-code", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}, "[json]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "dart.lineLength": 80, "dart.enableSdkFormatter": true, "dart.enableLint": true, "dart.showTodos": true, "dart.closingLabels": true, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true}