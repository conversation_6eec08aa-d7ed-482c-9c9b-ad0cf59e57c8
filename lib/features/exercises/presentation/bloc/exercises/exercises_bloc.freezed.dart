// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'exercises_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$ExercisesEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(String exerciseId) exerciseStarted,
    required TResult Function(String exerciseId, int score) exerciseCompleted,
    required TResult Function(String? filterType, int? filterDifficulty)
    filterChanged,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(String exerciseId)? exerciseStarted,
    TResult? Function(String exerciseId, int score)? exerciseCompleted,
    TResult? Function(String? filterType, int? filterDifficulty)? filterChanged,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(String exerciseId)? exerciseStarted,
    TResult Function(String exerciseId, int score)? exerciseCompleted,
    TResult Function(String? filterType, int? filterDifficulty)? filterChanged,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_ExerciseStarted value) exerciseStarted,
    required TResult Function(_ExerciseCompleted value) exerciseCompleted,
    required TResult Function(_FilterChanged value) filterChanged,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_ExerciseStarted value)? exerciseStarted,
    TResult? Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult? Function(_FilterChanged value)? filterChanged,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_ExerciseStarted value)? exerciseStarted,
    TResult Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult Function(_FilterChanged value)? filterChanged,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExercisesEventCopyWith<$Res> {
  factory $ExercisesEventCopyWith(
    ExercisesEvent value,
    $Res Function(ExercisesEvent) then,
  ) = _$ExercisesEventCopyWithImpl<$Res, ExercisesEvent>;
}

/// @nodoc
class _$ExercisesEventCopyWithImpl<$Res, $Val extends ExercisesEvent>
    implements $ExercisesEventCopyWith<$Res> {
  _$ExercisesEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadRequestedImplCopyWith<$Res> {
  factory _$$LoadRequestedImplCopyWith(
    _$LoadRequestedImpl value,
    $Res Function(_$LoadRequestedImpl) then,
  ) = __$$LoadRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadRequestedImplCopyWithImpl<$Res>
    extends _$ExercisesEventCopyWithImpl<$Res, _$LoadRequestedImpl>
    implements _$$LoadRequestedImplCopyWith<$Res> {
  __$$LoadRequestedImplCopyWithImpl(
    _$LoadRequestedImpl _value,
    $Res Function(_$LoadRequestedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadRequestedImpl implements _LoadRequested {
  const _$LoadRequestedImpl();

  @override
  String toString() {
    return 'ExercisesEvent.loadRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(String exerciseId) exerciseStarted,
    required TResult Function(String exerciseId, int score) exerciseCompleted,
    required TResult Function(String? filterType, int? filterDifficulty)
    filterChanged,
  }) {
    return loadRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(String exerciseId)? exerciseStarted,
    TResult? Function(String exerciseId, int score)? exerciseCompleted,
    TResult? Function(String? filterType, int? filterDifficulty)? filterChanged,
  }) {
    return loadRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(String exerciseId)? exerciseStarted,
    TResult Function(String exerciseId, int score)? exerciseCompleted,
    TResult Function(String? filterType, int? filterDifficulty)? filterChanged,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_ExerciseStarted value) exerciseStarted,
    required TResult Function(_ExerciseCompleted value) exerciseCompleted,
    required TResult Function(_FilterChanged value) filterChanged,
  }) {
    return loadRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_ExerciseStarted value)? exerciseStarted,
    TResult? Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult? Function(_FilterChanged value)? filterChanged,
  }) {
    return loadRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_ExerciseStarted value)? exerciseStarted,
    TResult Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult Function(_FilterChanged value)? filterChanged,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested(this);
    }
    return orElse();
  }
}

abstract class _LoadRequested implements ExercisesEvent {
  const factory _LoadRequested() = _$LoadRequestedImpl;
}

/// @nodoc
abstract class _$$ExerciseStartedImplCopyWith<$Res> {
  factory _$$ExerciseStartedImplCopyWith(
    _$ExerciseStartedImpl value,
    $Res Function(_$ExerciseStartedImpl) then,
  ) = __$$ExerciseStartedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String exerciseId});
}

/// @nodoc
class __$$ExerciseStartedImplCopyWithImpl<$Res>
    extends _$ExercisesEventCopyWithImpl<$Res, _$ExerciseStartedImpl>
    implements _$$ExerciseStartedImplCopyWith<$Res> {
  __$$ExerciseStartedImplCopyWithImpl(
    _$ExerciseStartedImpl _value,
    $Res Function(_$ExerciseStartedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? exerciseId = null}) {
    return _then(
      _$ExerciseStartedImpl(
        null == exerciseId
            ? _value.exerciseId
            : exerciseId // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ExerciseStartedImpl implements _ExerciseStarted {
  const _$ExerciseStartedImpl(this.exerciseId);

  @override
  final String exerciseId;

  @override
  String toString() {
    return 'ExercisesEvent.exerciseStarted(exerciseId: $exerciseId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseStartedImpl &&
            (identical(other.exerciseId, exerciseId) ||
                other.exerciseId == exerciseId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, exerciseId);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseStartedImplCopyWith<_$ExerciseStartedImpl> get copyWith =>
      __$$ExerciseStartedImplCopyWithImpl<_$ExerciseStartedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(String exerciseId) exerciseStarted,
    required TResult Function(String exerciseId, int score) exerciseCompleted,
    required TResult Function(String? filterType, int? filterDifficulty)
    filterChanged,
  }) {
    return exerciseStarted(exerciseId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(String exerciseId)? exerciseStarted,
    TResult? Function(String exerciseId, int score)? exerciseCompleted,
    TResult? Function(String? filterType, int? filterDifficulty)? filterChanged,
  }) {
    return exerciseStarted?.call(exerciseId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(String exerciseId)? exerciseStarted,
    TResult Function(String exerciseId, int score)? exerciseCompleted,
    TResult Function(String? filterType, int? filterDifficulty)? filterChanged,
    required TResult orElse(),
  }) {
    if (exerciseStarted != null) {
      return exerciseStarted(exerciseId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_ExerciseStarted value) exerciseStarted,
    required TResult Function(_ExerciseCompleted value) exerciseCompleted,
    required TResult Function(_FilterChanged value) filterChanged,
  }) {
    return exerciseStarted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_ExerciseStarted value)? exerciseStarted,
    TResult? Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult? Function(_FilterChanged value)? filterChanged,
  }) {
    return exerciseStarted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_ExerciseStarted value)? exerciseStarted,
    TResult Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult Function(_FilterChanged value)? filterChanged,
    required TResult orElse(),
  }) {
    if (exerciseStarted != null) {
      return exerciseStarted(this);
    }
    return orElse();
  }
}

abstract class _ExerciseStarted implements ExercisesEvent {
  const factory _ExerciseStarted(final String exerciseId) =
      _$ExerciseStartedImpl;

  String get exerciseId;

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseStartedImplCopyWith<_$ExerciseStartedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ExerciseCompletedImplCopyWith<$Res> {
  factory _$$ExerciseCompletedImplCopyWith(
    _$ExerciseCompletedImpl value,
    $Res Function(_$ExerciseCompletedImpl) then,
  ) = __$$ExerciseCompletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String exerciseId, int score});
}

/// @nodoc
class __$$ExerciseCompletedImplCopyWithImpl<$Res>
    extends _$ExercisesEventCopyWithImpl<$Res, _$ExerciseCompletedImpl>
    implements _$$ExerciseCompletedImplCopyWith<$Res> {
  __$$ExerciseCompletedImplCopyWithImpl(
    _$ExerciseCompletedImpl _value,
    $Res Function(_$ExerciseCompletedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? exerciseId = null, Object? score = null}) {
    return _then(
      _$ExerciseCompletedImpl(
        exerciseId: null == exerciseId
            ? _value.exerciseId
            : exerciseId // ignore: cast_nullable_to_non_nullable
                  as String,
        score: null == score
            ? _value.score
            : score // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc

class _$ExerciseCompletedImpl implements _ExerciseCompleted {
  const _$ExerciseCompletedImpl({
    required this.exerciseId,
    required this.score,
  });

  @override
  final String exerciseId;
  @override
  final int score;

  @override
  String toString() {
    return 'ExercisesEvent.exerciseCompleted(exerciseId: $exerciseId, score: $score)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseCompletedImpl &&
            (identical(other.exerciseId, exerciseId) ||
                other.exerciseId == exerciseId) &&
            (identical(other.score, score) || other.score == score));
  }

  @override
  int get hashCode => Object.hash(runtimeType, exerciseId, score);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseCompletedImplCopyWith<_$ExerciseCompletedImpl> get copyWith =>
      __$$ExerciseCompletedImplCopyWithImpl<_$ExerciseCompletedImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(String exerciseId) exerciseStarted,
    required TResult Function(String exerciseId, int score) exerciseCompleted,
    required TResult Function(String? filterType, int? filterDifficulty)
    filterChanged,
  }) {
    return exerciseCompleted(exerciseId, score);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(String exerciseId)? exerciseStarted,
    TResult? Function(String exerciseId, int score)? exerciseCompleted,
    TResult? Function(String? filterType, int? filterDifficulty)? filterChanged,
  }) {
    return exerciseCompleted?.call(exerciseId, score);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(String exerciseId)? exerciseStarted,
    TResult Function(String exerciseId, int score)? exerciseCompleted,
    TResult Function(String? filterType, int? filterDifficulty)? filterChanged,
    required TResult orElse(),
  }) {
    if (exerciseCompleted != null) {
      return exerciseCompleted(exerciseId, score);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_ExerciseStarted value) exerciseStarted,
    required TResult Function(_ExerciseCompleted value) exerciseCompleted,
    required TResult Function(_FilterChanged value) filterChanged,
  }) {
    return exerciseCompleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_ExerciseStarted value)? exerciseStarted,
    TResult? Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult? Function(_FilterChanged value)? filterChanged,
  }) {
    return exerciseCompleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_ExerciseStarted value)? exerciseStarted,
    TResult Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult Function(_FilterChanged value)? filterChanged,
    required TResult orElse(),
  }) {
    if (exerciseCompleted != null) {
      return exerciseCompleted(this);
    }
    return orElse();
  }
}

abstract class _ExerciseCompleted implements ExercisesEvent {
  const factory _ExerciseCompleted({
    required final String exerciseId,
    required final int score,
  }) = _$ExerciseCompletedImpl;

  String get exerciseId;
  int get score;

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseCompletedImplCopyWith<_$ExerciseCompletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilterChangedImplCopyWith<$Res> {
  factory _$$FilterChangedImplCopyWith(
    _$FilterChangedImpl value,
    $Res Function(_$FilterChangedImpl) then,
  ) = __$$FilterChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? filterType, int? filterDifficulty});
}

/// @nodoc
class __$$FilterChangedImplCopyWithImpl<$Res>
    extends _$ExercisesEventCopyWithImpl<$Res, _$FilterChangedImpl>
    implements _$$FilterChangedImplCopyWith<$Res> {
  __$$FilterChangedImplCopyWithImpl(
    _$FilterChangedImpl _value,
    $Res Function(_$FilterChangedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filterType = freezed,
    Object? filterDifficulty = freezed,
  }) {
    return _then(
      _$FilterChangedImpl(
        filterType: freezed == filterType
            ? _value.filterType
            : filterType // ignore: cast_nullable_to_non_nullable
                  as String?,
        filterDifficulty: freezed == filterDifficulty
            ? _value.filterDifficulty
            : filterDifficulty // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc

class _$FilterChangedImpl implements _FilterChanged {
  const _$FilterChangedImpl({this.filterType, this.filterDifficulty});

  @override
  final String? filterType;
  @override
  final int? filterDifficulty;

  @override
  String toString() {
    return 'ExercisesEvent.filterChanged(filterType: $filterType, filterDifficulty: $filterDifficulty)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterChangedImpl &&
            (identical(other.filterType, filterType) ||
                other.filterType == filterType) &&
            (identical(other.filterDifficulty, filterDifficulty) ||
                other.filterDifficulty == filterDifficulty));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filterType, filterDifficulty);

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterChangedImplCopyWith<_$FilterChangedImpl> get copyWith =>
      __$$FilterChangedImplCopyWithImpl<_$FilterChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(String exerciseId) exerciseStarted,
    required TResult Function(String exerciseId, int score) exerciseCompleted,
    required TResult Function(String? filterType, int? filterDifficulty)
    filterChanged,
  }) {
    return filterChanged(filterType, filterDifficulty);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(String exerciseId)? exerciseStarted,
    TResult? Function(String exerciseId, int score)? exerciseCompleted,
    TResult? Function(String? filterType, int? filterDifficulty)? filterChanged,
  }) {
    return filterChanged?.call(filterType, filterDifficulty);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(String exerciseId)? exerciseStarted,
    TResult Function(String exerciseId, int score)? exerciseCompleted,
    TResult Function(String? filterType, int? filterDifficulty)? filterChanged,
    required TResult orElse(),
  }) {
    if (filterChanged != null) {
      return filterChanged(filterType, filterDifficulty);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_ExerciseStarted value) exerciseStarted,
    required TResult Function(_ExerciseCompleted value) exerciseCompleted,
    required TResult Function(_FilterChanged value) filterChanged,
  }) {
    return filterChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_ExerciseStarted value)? exerciseStarted,
    TResult? Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult? Function(_FilterChanged value)? filterChanged,
  }) {
    return filterChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_ExerciseStarted value)? exerciseStarted,
    TResult Function(_ExerciseCompleted value)? exerciseCompleted,
    TResult Function(_FilterChanged value)? filterChanged,
    required TResult orElse(),
  }) {
    if (filterChanged != null) {
      return filterChanged(this);
    }
    return orElse();
  }
}

abstract class _FilterChanged implements ExercisesEvent {
  const factory _FilterChanged({
    final String? filterType,
    final int? filterDifficulty,
  }) = _$FilterChangedImpl;

  String? get filterType;
  int? get filterDifficulty;

  /// Create a copy of ExercisesEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterChangedImplCopyWith<_$FilterChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ExercisesState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExercisesStateCopyWith<$Res> {
  factory $ExercisesStateCopyWith(
    ExercisesState value,
    $Res Function(ExercisesState) then,
  ) = _$ExercisesStateCopyWithImpl<$Res, ExercisesState>;
}

/// @nodoc
class _$ExercisesStateCopyWithImpl<$Res, $Val extends ExercisesState>
    implements $ExercisesStateCopyWith<$Res> {
  _$ExercisesStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
    _$InitialImpl value,
    $Res Function(_$InitialImpl) then,
  ) = __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ExercisesStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
    _$InitialImpl _value,
    $Res Function(_$InitialImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ExercisesState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements ExercisesState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
    _$LoadingImpl value,
    $Res Function(_$LoadingImpl) then,
  ) = __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$ExercisesStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
    _$LoadingImpl _value,
    $Res Function(_$LoadingImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'ExercisesState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements ExercisesState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
    _$LoadedImpl value,
    $Res Function(_$LoadedImpl) then,
  ) = __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({
    List<Exercise> exercises,
    List<Exercise> filteredExercises,
    List<GameCategoryModel> categories,
    String? currentFilter,
    int? difficultyFilter,
  });
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$ExercisesStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
    _$LoadedImpl _value,
    $Res Function(_$LoadedImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exercises = null,
    Object? filteredExercises = null,
    Object? categories = null,
    Object? currentFilter = freezed,
    Object? difficultyFilter = freezed,
  }) {
    return _then(
      _$LoadedImpl(
        exercises: null == exercises
            ? _value._exercises
            : exercises // ignore: cast_nullable_to_non_nullable
                  as List<Exercise>,
        filteredExercises: null == filteredExercises
            ? _value._filteredExercises
            : filteredExercises // ignore: cast_nullable_to_non_nullable
                  as List<Exercise>,
        categories: null == categories
            ? _value._categories
            : categories // ignore: cast_nullable_to_non_nullable
                  as List<GameCategoryModel>,
        currentFilter: freezed == currentFilter
            ? _value.currentFilter
            : currentFilter // ignore: cast_nullable_to_non_nullable
                  as String?,
        difficultyFilter: freezed == difficultyFilter
            ? _value.difficultyFilter
            : difficultyFilter // ignore: cast_nullable_to_non_nullable
                  as int?,
      ),
    );
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl({
    required final List<Exercise> exercises,
    required final List<Exercise> filteredExercises,
    required final List<GameCategoryModel> categories,
    this.currentFilter,
    this.difficultyFilter,
  }) : _exercises = exercises,
       _filteredExercises = filteredExercises,
       _categories = categories;

  final List<Exercise> _exercises;
  @override
  List<Exercise> get exercises {
    if (_exercises is EqualUnmodifiableListView) return _exercises;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exercises);
  }

  final List<Exercise> _filteredExercises;
  @override
  List<Exercise> get filteredExercises {
    if (_filteredExercises is EqualUnmodifiableListView)
      return _filteredExercises;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredExercises);
  }

  final List<GameCategoryModel> _categories;
  @override
  List<GameCategoryModel> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  @override
  final String? currentFilter;
  @override
  final int? difficultyFilter;

  @override
  String toString() {
    return 'ExercisesState.loaded(exercises: $exercises, filteredExercises: $filteredExercises, categories: $categories, currentFilter: $currentFilter, difficultyFilter: $difficultyFilter)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(
              other._exercises,
              _exercises,
            ) &&
            const DeepCollectionEquality().equals(
              other._filteredExercises,
              _filteredExercises,
            ) &&
            const DeepCollectionEquality().equals(
              other._categories,
              _categories,
            ) &&
            (identical(other.currentFilter, currentFilter) ||
                other.currentFilter == currentFilter) &&
            (identical(other.difficultyFilter, difficultyFilter) ||
                other.difficultyFilter == difficultyFilter));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_exercises),
    const DeepCollectionEquality().hash(_filteredExercises),
    const DeepCollectionEquality().hash(_categories),
    currentFilter,
    difficultyFilter,
  );

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) {
    return loaded(
      exercises,
      filteredExercises,
      categories,
      currentFilter,
      difficultyFilter,
    );
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(
      exercises,
      filteredExercises,
      categories,
      currentFilter,
      difficultyFilter,
    );
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(
        exercises,
        filteredExercises,
        categories,
        currentFilter,
        difficultyFilter,
      );
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements ExercisesState {
  const factory _Loaded({
    required final List<Exercise> exercises,
    required final List<Exercise> filteredExercises,
    required final List<GameCategoryModel> categories,
    final String? currentFilter,
    final int? difficultyFilter,
  }) = _$LoadedImpl;

  List<Exercise> get exercises;
  List<Exercise> get filteredExercises;
  List<GameCategoryModel> get categories;
  String? get currentFilter;
  int? get difficultyFilter;

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ExerciseInProgressImplCopyWith<$Res> {
  factory _$$ExerciseInProgressImplCopyWith(
    _$ExerciseInProgressImpl value,
    $Res Function(_$ExerciseInProgressImpl) then,
  ) = __$$ExerciseInProgressImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Exercise exercise});

  $ExerciseCopyWith<$Res> get exercise;
}

/// @nodoc
class __$$ExerciseInProgressImplCopyWithImpl<$Res>
    extends _$ExercisesStateCopyWithImpl<$Res, _$ExerciseInProgressImpl>
    implements _$$ExerciseInProgressImplCopyWith<$Res> {
  __$$ExerciseInProgressImplCopyWithImpl(
    _$ExerciseInProgressImpl _value,
    $Res Function(_$ExerciseInProgressImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? exercise = null}) {
    return _then(
      _$ExerciseInProgressImpl(
        null == exercise
            ? _value.exercise
            : exercise // ignore: cast_nullable_to_non_nullable
                  as Exercise,
      ),
    );
  }

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ExerciseCopyWith<$Res> get exercise {
    return $ExerciseCopyWith<$Res>(_value.exercise, (value) {
      return _then(_value.copyWith(exercise: value));
    });
  }
}

/// @nodoc

class _$ExerciseInProgressImpl implements _ExerciseInProgress {
  const _$ExerciseInProgressImpl(this.exercise);

  @override
  final Exercise exercise;

  @override
  String toString() {
    return 'ExercisesState.exerciseInProgress(exercise: $exercise)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseInProgressImpl &&
            (identical(other.exercise, exercise) ||
                other.exercise == exercise));
  }

  @override
  int get hashCode => Object.hash(runtimeType, exercise);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseInProgressImplCopyWith<_$ExerciseInProgressImpl> get copyWith =>
      __$$ExerciseInProgressImplCopyWithImpl<_$ExerciseInProgressImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) {
    return exerciseInProgress(exercise);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) {
    return exerciseInProgress?.call(exercise);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (exerciseInProgress != null) {
      return exerciseInProgress(exercise);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) {
    return exerciseInProgress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) {
    return exerciseInProgress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (exerciseInProgress != null) {
      return exerciseInProgress(this);
    }
    return orElse();
  }
}

abstract class _ExerciseInProgress implements ExercisesState {
  const factory _ExerciseInProgress(final Exercise exercise) =
      _$ExerciseInProgressImpl;

  Exercise get exercise;

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseInProgressImplCopyWith<_$ExerciseInProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
    _$ErrorImpl value,
    $Res Function(_$ErrorImpl) then,
  ) = __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$ExercisesStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
    _$ErrorImpl _value,
    $Res Function(_$ErrorImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null}) {
    return _then(
      _$ErrorImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ExercisesState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )
    loaded,
    required TResult Function(Exercise exercise) exerciseInProgress,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult? Function(Exercise exercise)? exerciseInProgress,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
      List<Exercise> exercises,
      List<Exercise> filteredExercises,
      List<GameCategoryModel> categories,
      String? currentFilter,
      int? difficultyFilter,
    )?
    loaded,
    TResult Function(Exercise exercise)? exerciseInProgress,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_ExerciseInProgress value) exerciseInProgress,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_ExerciseInProgress value)? exerciseInProgress,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements ExercisesState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of ExercisesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
