import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/models/game_category_model.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../data/services/game_category_service.dart';
import '../../../domain/entities/exercise.dart';

part 'exercises_bloc.freezed.dart';
part 'exercises_event.dart';
part 'exercises_state.dart';

class ExercisesBloc extends Bloc<ExercisesEvent, ExercisesState> {
  ExercisesBloc() : super(const _Initial()) {
    on<ExercisesEvent>((event, emit) async {
      try {
        if (event is _LoadRequested) {
          emit(const _Loading());

          final futures = await Future.wait([
            _loadCategories(),
            _loadExercises(),
          ]);

          final categories = futures[0] as List<GameCategoryModel>;
          final exercises = futures[1] as List<Exercise>;

          emit(
            _Loaded(
              exercises: exercises,
              filteredExercises: exercises,
              categories: categories,
              currentFilter: null,
              difficultyFilter: null,
            ),
          );
        }

        if (event is _ExerciseStarted) {
          if (state is _Loaded) {
            final currentState = state as _Loaded;
            final exercise = currentState.exercises.firstWhere(
              (ex) => ex.id == event.exerciseId,
            );

            emit(_ExerciseInProgress(exercise));
          }
        }

        if (event is _ExerciseCompleted) {
          if (state is _ExerciseInProgress) {
            // Simulate saving result
            await Future.delayed(const Duration(milliseconds: 500));

            // Reload exercises with updated data
            add(const ExercisesEvent.loadRequested());
          }
        }

        if (event is _FilterChanged) {
          if (state is _Loaded) {
            final currentState = state as _Loaded;

            List<Exercise> filteredExercises = currentState.exercises;

            if (event.filterType != null && event.filterType != 'all') {
              final selectedCategory = currentState.categories.firstWhere(
                (cat) => cat.id == event.filterType,
                orElse: () =>
                    const GameCategoryModel(id: '', name: '', description: ''),
              );

              if (selectedCategory.name.isNotEmpty) {
                filteredExercises = filteredExercises
                    .where(
                      (exercise) =>
                          exercise.categoryName?.toLowerCase() ==
                          selectedCategory.name.toLowerCase(),
                    )
                    .toList();
              }
            }

            if (event.filterDifficulty != null) {
              filteredExercises = filteredExercises
                  .where(
                    (exercise) => exercise.difficulty == event.filterDifficulty,
                  )
                  .toList();
            }

            emit(
              _Loaded(
                exercises: currentState.exercises,
                filteredExercises: filteredExercises,
                categories: currentState.categories,
                currentFilter: event.filterType,
                difficultyFilter: event.filterDifficulty,
              ),
            );
          }
        }
      } on Exception catch (e) {
        emit(_Error(e.toString()));
      }
    });
  }

  Future<List<GameCategoryModel>> _loadCategories() async {
    try {
      final hiveUserService = HiveUserService();
      final currentUser = await hiveUserService.getCurrentUser();
      final accessToken = currentUser?.accessToken;

      final categories = await GameCategoryService.getGameCategories(
        accessToken: accessToken,
      );

      if (categories != null) {
        return categories;
      }
    } catch (e) {
      print('Error loading categories: $e');
    }

    // Return default categories if API fails
    return [
      const GameCategoryModel(
        id: 'all',
        name: 'All',
        description: 'All exercises',
        iconName: 'apps',
        colorCode: '#6366F1',
      ),
      const GameCategoryModel(
        id: 'memory',
        name: 'Memory',
        description: 'Memory exercises',
        iconName: 'memory',
        colorCode: '#8B5CF6',
      ),
      const GameCategoryModel(
        id: 'logic',
        name: 'Logic',
        description: 'Logic exercises',
        iconName: 'calculate',
        colorCode: '#06B6D4',
      ),
      const GameCategoryModel(
        id: 'verbal',
        name: 'Verbal',
        description: 'Verbal exercises',
        iconName: 'record_voice_over',
        colorCode: '#F59E0B',
      ),
    ];
  }

  Future<List<Exercise>> _loadExercises() async {
    try {
      final hiveUserService = HiveUserService();
      final currentUser = await hiveUserService.getCurrentUser();
      final accessToken = currentUser?.accessToken;
      final partyId = currentUser?.partyId;
      final partyType = currentUser?.partyTypeKey;

      if (accessToken == null || partyId == null || partyType == null) {
        throw Exception("User not logged in or missing data");
      }

      final assignedGames = await AssignedGamesInfoService.getAssignedGames(
        tenantCode: currentUser?.tenantCode ?? '',
        accessToken: accessToken,
        partyId: partyId,
        partyTypeKey: partyType,
      );

      if (assignedGames == null) {
        return [];
      }

      return assignedGames.map((game) {
        return Exercise(
          id: game.gameId ?? '',
          title: game.gameName?.trim() ?? 'Unknown Game',
          description: game.description?.trim() ?? '',
          type: _inferType(game.categoryName),
          categoryName: game.categoryName,
          difficulty: _parseDifficultyLevel(game.difficultyLevel),
          estimatedDuration: game.estimatedDuration ?? 0,
          iconPath: game.thumbnailUrl?.isNotEmpty == true
              ? game.thumbnailUrl!
              : 'assets/icons/default_game.png',
          isCompleted: game.isCompleted ?? false,
          lastScore: game.lastScore,
          lastPlayedAt: game.lastPlayedAt,
        );
      }).toList();
    } catch (e) {
      print("Error loading exercises: $e");
      return [];
    }
  }
}

int _parseDifficultyLevel(String? difficultyLevel) {
  switch (difficultyLevel?.toLowerCase()) {
    case 'easy':
      return 1;
    case 'medium':
      return 3;
    case 'hard':
      return 4;
    case 'expert':
      return 5;
    default:
      return 2;
  }
}

String _inferType(String? categoryName) {
  switch (categoryName?.toLowerCase()) {
    case 'sequence memory':
      return 'memory';
    case 'logic':
      return 'logic';
    case 'verbal':
      return 'verbal';
    case 'pattern':
      return 'pattern';
    default:
      return 'general';
  }
}
