part of 'exercises_bloc.dart';

@freezed
class ExercisesState with _$ExercisesState {
  const factory ExercisesState.initial() = _Initial;
  const factory ExercisesState.loading() = _Loading;
  const factory ExercisesState.loaded({
    required List<Exercise> exercises,
    required List<Exercise> filteredExercises,
    required List<GameCategoryModel> categories,
    String? currentFilter,
    int? difficultyFilter,
  }) = _Loaded;
  const factory ExercisesState.exerciseInProgress(Exercise exercise) =
      _ExerciseInProgress;
  const factory ExercisesState.error(String message) = _Error;
}
