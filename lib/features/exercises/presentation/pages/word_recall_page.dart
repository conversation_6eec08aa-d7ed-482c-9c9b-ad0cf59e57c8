import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/constants/game_constants.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum ExercisePhase {
  instructions,
  memorization,
  recall,
  roundResults,
  finalResults,
}

class RoundResult {
  final int round;
  final int score;
  final int correctSelections;
  final int incorrectSelections;
  final int missedWords;
  final double percentage;
  final List<String> targetWords;
  final Set<String> selectedWords;

  RoundResult({
    required this.round,
    required this.score,
    required this.correctSelections,
    required this.incorrectSelections,
    required this.missedWords,
    required this.percentage,
    required this.targetWords,
    required this.selectedWords,
  });

  Color get scoreColor {
    if (percentage >= 80) {
      return AppTheme.successColor;
    } else if (percentage >= 60) {
      return AppTheme.accentColor;
    } else {
      return AppTheme.warningColor;
    }
  }

  String get scoreMessage {
    if (percentage == 100) {
      return 'Excellent memory!';
    } else {
      return 'Keep practicing!';
    }
  }

  IconData get scoreIcon {
    if (percentage >= 80) {
      return Icons.star;
    } else if (percentage >= 60) {
      return Icons.thumb_up;
    } else {
      return Icons.trending_up;
    }
  }
}

class WordRecallPage extends StatefulWidget {
  const WordRecallPage({super.key});

  @override
  State<WordRecallPage> createState() => _WordRecallPageState();
}

class _WordRecallPageState extends State<WordRecallPage> {
  // Exercise state
  ExercisePhase _currentPhase = ExercisePhase.instructions;
  List<String> _targetWords = [];
  List<String> _allWords = [];
  final Set<String> _selectedWords = {};
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  int timer = 0;
  String _gameId = "";
  String _instructions = '';
  final int _minCorrect = 5;
  // Multi-round state
  int _currentRound = 1;
  final int _totalRounds = 1;
  int _totalScore = 0;
  int _targetScore = 0;
  int _scoreEarned = 0;
  int _rewardPoints = 0;
  String _levelId = '';
  final List<RoundResult> _roundResults = [];
  String gameInstructions = '';
  // Word pools
  String? _sessionId;

  @override
  void initState() {
    super.initState();
    _generateWords();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _generateWords() async {
    final random = Random();
    _targetWords.clear();
    _allWords.clear();
    _selectedWords.clear();
    final loadedGame = await HiveUserService().getAssignedGames();
    // Use words from loaded game config if available

    // wordsToUse = loadedGame.assets.words!;
    gameInstructions = loadedGame?.instructions ?? '';
    timer = loadedGame?.timer ?? 8;

    final difficulty = 'medium';

    // Get words from constants instead of service
    final wordPool = List<String>.from(
      GameConstants.wordRecallData[difficulty] ?? [],
    );
    wordPool.shuffle();
    List<String> wordsToUse = wordPool;
    setState(() {
      _instructions = loadedGame!.instructions;
      _timeRemaining = loadedGame.timer!;
      _gameId = loadedGame.id;
      // _minCorrect = loadedGame.logic!.minCorrect;
      _targetScore = loadedGame.targetScore ?? 0;
      _rewardPoints = loadedGame.rewards.points ?? 0;
      _levelId = loadedGame.levelId ?? '1';
    });

    // Select 5 random target words
    final shuffledPool = List<String>.from(wordsToUse)..shuffle(random);
    _targetWords = shuffledPool.take(_minCorrect).toList();

    // Remove target words from pool for distractors
    final distractorPool = List<String>.from(wordsToUse)
      ..removeWhere((w) => _targetWords.contains(w));
    distractorPool.shuffle(random);
    final distractorCount = 3; // You can adjust this number
    final distractors = distractorPool.take(distractorCount).toList();

    _allWords = [..._targetWords, ...distractors]..shuffle(random);
  }

  Future<void> _startMemorization() async {
    setState(() {
      _currentPhase = ExercisePhase.memorization;
      _timeRemaining = timer;
      _instructions = gameInstructions;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });
      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecall();
      }
    });
    final activeSessions = await AssignedGamesInfoService.getActiveSession(
      _gameId,
    );
    print(activeSessions);
    print(activeSessions?['details']);
    if (activeSessions?['details'] == "UNFINISHED_SESSION_FOUND") {
      final shouldContinue = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Resume Game?"),
          content: Text(
            "You have an unfinished session. Do you want to continue?",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text("No"),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text("Yes"),
            ),
          ],
        ),
      );

      if (shouldContinue == true) {
        setState(() {
          _sessionId = activeSessions?['session_ids'][0];
        });
      } else {
        final config = await AssignedGamesInfoService.startGame(
          gameId: _gameId,
          levelId: _levelId,
        );
        setState(() {
          _sessionId = config?['session_id'];
        });
      }
    } else {
      final config = await AssignedGamesInfoService.startGame(
        gameId: _gameId,
        levelId: _levelId,
      );
      setState(() {
        _sessionId = config?['session_id'];
      });
    }

    // if (activeSessions?['details'] == "UNFINISHED_SESSION_FOUND") {
    //   print(activeSessions);
    //   setState(() {
    //     _sessionId = activeSessions?['session_ids'][0];
    //   });
    // } else {
    //   final config = await AssignedGamesInfoService.startGame(
    //     gameId: _gameId,
    //     levelId: _levelId,
    //   );
    //   setState(() {
    //     _sessionId = config?['session_id'];
    //   });
    // }
  }

  void _startRecall() {
    setState(() {
      _currentPhase = ExercisePhase.recall;
      _instructions = gameInstructions;
    });
  }

  void _toggleWordSelection(String word) {
    setState(() {
      if (_selectedWords.contains(word)) {
        _selectedWords.remove(word);
      } else {
        if (_selectedWords.length < _minCorrect) {
          _selectedWords.add(word);
        }
      }
    });
  }

  Future<void> _submitAnswer() async {
    int correctSelections = 0;
    for (String word in _selectedWords) {
      if (_targetWords.contains(word)) {
        correctSelections++;
      }
    }

    // Calculate score (correct selections - incorrect selections, minimum 0)
    int incorrectSelections = _selectedWords.length - correctSelections;
    _score = (correctSelections - incorrectSelections).clamp(0, 5);

    // Calculate percentage for this round
    double roundPercentage = (correctSelections / _targetWords.length) * 100;

    // Save round result
    _roundResults.add(
      RoundResult(
        round: _currentRound,
        score: _score,
        correctSelections: correctSelections,
        incorrectSelections: incorrectSelections,
        missedWords: _targetWords.length - correctSelections,
        percentage: roundPercentage,
        targetWords: List.from(_targetWords),
        selectedWords: Set.from(_selectedWords),
      ),
    );

    _totalScore += _score;

    if (_currentRound < _totalRounds) {
      // Show round results and continue to next round
      setState(() {
        _currentPhase = ExercisePhase.roundResults;
      });
    } else {
      // Show final results
      setState(() {
        _currentPhase = ExercisePhase.finalResults;
      });
    }

    final config = await AssignedGamesInfoService.endGame(
      gameId: _gameId,
      levelId: _levelId,
      sessionId: _sessionId!,
      score: correctSelections == _minCorrect ? _rewardPoints.toString() : '0',
    );
  }

  void _continueToNextRound() {
    _currentRound++;
    _generateWords();
    setState(() {
      _currentPhase = ExercisePhase.memorization;
      _score = 0;
      _timeRemaining = 8;
    });
    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });
      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecall();
      }
    });
  }

  void _restartExercise() {
    _generateWords();
    setState(() {
      _currentPhase = ExercisePhase.instructions;
      _score = 0;
      _currentRound = 1;
      _totalScore = 0;
      _roundResults.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Word Recall Exercise',
          style: AppTheme.headlineSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        backgroundColor: AppTheme.secondaryColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              context.go(AppRouter.exercises);
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildWebLayout(),
      ),
    );
  }

  Widget _buildWebLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 1000,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: MediaQuery.of(context).size.height - 100,
          ),
          padding: EdgeInsets.all(24),
          child: SingleChildScrollView(child: _buildExerciseContent()),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case ExercisePhase.instructions:
        return _buildInstructionsPhase();
      case ExercisePhase.memorization:
        return _buildMemorizationPhase();
      case ExercisePhase.recall:
        return _buildRecallPhase();
      case ExercisePhase.roundResults:
        return _buildRoundResultsPhase();
      case ExercisePhase.finalResults:
        return _buildFinalResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.record_voice_over,
            size: 60.sp,
            color: AppTheme.secondaryColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Word Recall Exercise',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                gameInstructions.trim().isEmpty
                    ? '1. Complete 5 rounds of word recall\n'
                          '2. Each round: memorize 5 words for 8 seconds\n'
                          '3. Then select the words you remember\n'
                          '4. Try to get at least 4 correct in each round!'
                    : _instructions,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startMemorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemorizationPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.timer, color: AppTheme.primaryColor, size: 24.sp),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds - Memorize these words: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                  fontSize: 10.sp,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: _targetWords.map((word) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: AppTheme.spacing8),
                child: ResponsiveText(
                  word.toUpperCase(),
                  style: AppTheme.headlineMedium.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimary,
                    letterSpacing: 2,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }).toList(),
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Focus and try to remember all the words!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.secondaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.secondaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(Icons.quiz, color: AppTheme.secondaryColor, size: 32.sp),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds - Select the words you remember',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Tap on the words you saw earlier (${_selectedWords.length}/5 selected)',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        GridView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppTheme.spacing12,
            mainAxisSpacing: AppTheme.spacing12,
            childAspectRatio: 2.5,
          ),
          itemCount: _allWords.length,
          itemBuilder: (context, index) {
            final word = _allWords[index];
            final isSelected = _selectedWords.contains(word);

            return GestureDetector(
              onTap: () => _toggleWordSelection(word),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: EdgeInsets.all(AppTheme.spacing16),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme.primaryColor.withOpacity(0.2)
                      : AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.dividerColor,
                    width: isSelected ? 2.w : 1.w,
                  ),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: AppTheme.primaryColor.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Center(
                  child: ResponsiveText(
                    word.toUpperCase(),
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.textPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            );
          },
        ),
        SizedBox(height: AppTheme.spacing16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _selectedWords.isNotEmpty ? _submitAnswer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.secondaryColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoundResultsPhase() {
    final currentRoundResult = _roundResults.last;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: currentRoundResult.scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            currentRoundResult.scoreIcon,
            size: 60.sp,
            color: currentRoundResult.scoreColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Round ${currentRoundResult.round} Complete!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: currentRoundResult.scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: currentRoundResult.scoreColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${currentRoundResult.percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: currentRoundResult.scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                currentRoundResult.scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              Divider(color: currentRoundResult.scoreColor.withOpacity(0.3)),
              SizedBox(height: AppTheme.spacing16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildScoreStat(
                    icon: Icons.check_circle,
                    label: 'Correct',
                    value: '${currentRoundResult.correctSelections}',
                    color: AppTheme.successColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.cancel,
                    label: 'Incorrect',
                    value: '${currentRoundResult.incorrectSelections}',
                    color: AppTheme.errorColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.help_outline,
                    label: 'Missed',
                    value: '${currentRoundResult.missedWords}',
                    color: AppTheme.warningColor,
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        _buildWordReview(
          currentRoundResult.targetWords,
          currentRoundResult.selectedWords,
        ),
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _continueToNextRound,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.secondaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Continue to Next Round',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinalResultsPhase() {
    double averagePercentage = _roundResults.isNotEmpty
        ? _roundResults.map((r) => r.percentage).reduce((a, b) => a + b) /
              _roundResults.length
        : 0;
    int averageScore = _roundResults.isNotEmpty
        ? _roundResults.map((r) => r.score).reduce((a, b) => a + b) ~/
              _roundResults.length
        : 0;

    // Check if user achieved minimum correct answers
    int totalCorrectSelections = _roundResults
        .map((r) => r.correctSelections)
        .reduce((a, b) => a + b);

    bool achievedMinCorrect = totalCorrectSelections >= (_minCorrect ?? 1);
    int finalScore = achievedMinCorrect ? (_targetScore ?? 0) : 0;
    setState(() {
      _scoreEarned = achievedMinCorrect ? (_targetScore ?? 0) : 0;
    });
    int rewardPoints = achievedMinCorrect ? (_rewardPoints ?? 0) : 0;

    int totalMissedWords = _roundResults
        .map((r) => r.missedWords)
        .reduce((a, b) => a + b);

    Color finalScoreColor;
    String finalScoreMessage;
    IconData finalScoreIcon;

    if (averagePercentage == 100) {
      finalScoreColor = AppTheme.successColor;
      finalScoreMessage = 'Excellent memory!';
      finalScoreIcon = Icons.star;
    } else {
      finalScoreColor = AppTheme.warningColor;
      finalScoreMessage = 'Keep practicing!';
      finalScoreIcon = Icons.trending_up;
    }

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        vertical: AppTheme.spacing24,
        horizontal: AppTheme.spacing16,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
            ),
            child: Icon(finalScoreIcon, size: 60.sp, color: finalScoreColor),
          ),
          SizedBox(height: AppTheme.spacing24),
          ResponsiveText(
            '',
            style: AppTheme.displaySmall.copyWith(
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppTheme.spacing16),
          Container(
            padding: EdgeInsets.all(AppTheme.spacing24),
            decoration: BoxDecoration(
              color: finalScoreColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
              border: Border.all(
                color: finalScoreColor.withOpacity(0.3),
                width: 2.w,
              ),
            ),
            child: Column(
              children: [
                ResponsiveText(
                  'You got $_targetScore  points',
                  style: AppTheme.displayLarge.copyWith(
                    fontWeight: FontWeight.w800,
                    color: finalScoreColor,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),

                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  finalScoreMessage,
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                // Show reward if achieved minimum correct
                if (achievedMinCorrect && rewardPoints > 0) ...[
                  SizedBox(height: AppTheme.spacing16),
                  Divider(color: finalScoreColor.withOpacity(0.3)),
                  SizedBox(height: AppTheme.spacing16),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing16,
                      vertical: AppTheme.spacing12,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(
                        AppTheme.radiusMedium,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.stars,
                          color: AppTheme.accentColor,
                          size: 24.sp,
                        ),
                        SizedBox(width: AppTheme.spacing8),
                        ResponsiveText(
                          'Reward: $rewardPoints points',
                          style: AppTheme.titleMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.accentColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                SizedBox(height: AppTheme.spacing16),
                Divider(color: finalScoreColor.withOpacity(0.3)),
                SizedBox(height: AppTheme.spacing16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildScoreStat(
                      icon: Icons.check_circle,
                      label: 'Total Correct',
                      value: '$totalCorrectSelections',
                      color: AppTheme.successColor,
                    ),
                    _buildScoreStat(
                      icon: Icons.help_outline,
                      label: 'Total Missed',
                      value: '$totalMissedWords',
                      color: AppTheme.warningColor,
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(height: AppTheme.spacing24),
          // _buildRoundBreakdown(),
          SizedBox(height: AppTheme.spacing32),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    // Navigate back to exercises page
                    context.go(AppRouter.exercises);
                  },
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                    side: BorderSide(color: AppTheme.textSecondary),
                  ),
                  child: ResponsiveText(
                    'Back to Exercises',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppTheme.spacing16),
              Expanded(
                child: ElevatedButton(
                  onPressed: _restartExercise,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.secondaryColor,
                    padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  ),
                  child: ResponsiveText(
                    'Try Again',
                    style: AppTheme.labelLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textOnPrimary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.sp),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.labelSmall.copyWith(color: AppTheme.textSecondary),
        ),
      ],
    );
  }

  Widget _buildRoundBreakdown() {
    return SizedBox(
      height: 200,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText(
              'Round-by-Round Breakdown',
              style: AppTheme.titleSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
              ),
            ),
            SizedBox(height: AppTheme.spacing12),
            ..._roundResults.map(
              (result) => Container(
                margin: EdgeInsets.only(bottom: AppTheme.spacing8),
                padding: EdgeInsets.all(AppTheme.spacing12),
                decoration: BoxDecoration(
                  color: result.scoreColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: result.scoreColor.withOpacity(0.3),
                    width: 1.w,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32.w,
                      height: 32.h,
                      decoration: BoxDecoration(
                        color: result.scoreColor,
                        borderRadius: BorderRadius.circular(
                          AppTheme.radiusSmall,
                        ),
                      ),
                      child: Center(
                        child: ResponsiveText(
                          '${result.round}',
                          style: AppTheme.labelMedium.copyWith(
                            color: AppTheme.textOnPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: AppTheme.spacing12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ResponsiveText(
                            'Round ${result.round}',
                            style: AppTheme.labelMedium.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          ResponsiveText(
                            '${result.percentage.toInt()}% • Score: ${result.score}/5',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      result.scoreIcon,
                      color: result.scoreColor,
                      size: 20.sp,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWordReview(List<String> targetWords, Set<String> selectedWords) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: AppTheme.dividerColor, width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Word Review',
            style: AppTheme.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          SizedBox(height: AppTheme.spacing12),
          Wrap(
            spacing: AppTheme.spacing8,
            runSpacing: AppTheme.spacing8,
            children: targetWords.map((word) {
              final wasSelected = selectedWords.contains(word);
              return Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing12,
                  vertical: 6.h,
                ),
                decoration: BoxDecoration(
                  color: wasSelected
                      ? AppTheme.successColor.withOpacity(0.1)
                      : AppTheme.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: wasSelected
                        ? AppTheme.successColor
                        : AppTheme.errorColor,
                    width: 1.w,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      wasSelected ? Icons.check : Icons.close,
                      color: wasSelected
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                      size: 16.sp,
                    ),
                    SizedBox(width: AppTheme.spacing4),
                    ResponsiveText(
                      word,
                      style: AppTheme.labelMedium.copyWith(
                        color: wasSelected
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
