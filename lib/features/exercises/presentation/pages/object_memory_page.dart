import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum ObjectMemoryPhase {
  instructions,
  memorization,
  recall,
  results,
}

class ObjectMemoryPage extends StatefulWidget {
  const ObjectMemoryPage({super.key});

  @override
  State<ObjectMemoryPage> createState() => _ObjectMemoryPageState();
}

class _ObjectMemoryPageState extends State<ObjectMemoryPage> {
  // Exercise state
  ObjectMemoryPhase _currentPhase = ObjectMemoryPhase.instructions;
  List<ObjectItem> _targetObjects = [];
  List<ObjectItem> _allObjects = [];
  Set<String> _selectedObjects = {};
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  
  // Object pool with everyday items
  final List<ObjectItem> _objectPool = [
    ObjectItem(id: 'cup', name: 'Cup', icon: Icons.coffee, color: AppTheme.primaryColor),
    ObjectItem(id: 'spoon', name: 'Spoon', icon: Icons.restaurant, color: AppTheme.secondaryColor),
    ObjectItem(id: 'key', name: 'Key', icon: Icons.key, color: AppTheme.accentColor),
    ObjectItem(id: 'book', name: 'Book', icon: Icons.book, color: AppTheme.warningColor),
    ObjectItem(id: 'phone', name: 'Phone', icon: Icons.phone, color: AppTheme.successColor),
    ObjectItem(id: 'watch', name: 'Watch', icon: Icons.watch, color: AppTheme.errorColor),
    ObjectItem(id: 'glasses', name: 'Glasses', icon: Icons.visibility, color: AppTheme.primaryColor),
    ObjectItem(id: 'wallet', name: 'Wallet', icon: Icons.account_balance_wallet, color: AppTheme.secondaryColor),
    ObjectItem(id: 'pen', name: 'Pen', icon: Icons.edit, color: AppTheme.accentColor),
    ObjectItem(id: 'lamp', name: 'Lamp', icon: Icons.lightbulb, color: AppTheme.warningColor),
    ObjectItem(id: 'clock', name: 'Clock', icon: Icons.access_time, color: AppTheme.successColor),
    ObjectItem(id: 'camera', name: 'Camera', icon: Icons.camera_alt, color: AppTheme.errorColor),
    ObjectItem(id: 'headphones', name: 'Headphones', icon: Icons.headphones, color: AppTheme.primaryColor),
    ObjectItem(id: 'umbrella', name: 'Umbrella', icon: Icons.beach_access, color: AppTheme.secondaryColor),
    ObjectItem(id: 'bag', name: 'Bag', icon: Icons.shopping_bag, color: AppTheme.accentColor),
  ];

  @override
  void initState() {
    super.initState();
    _generateObjects();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _generateObjects() {
    final random = Random();
    _targetObjects.clear();
    _allObjects.clear();
    _selectedObjects.clear();
    
    // Select 6 random target objects
    final shuffledPool = List<ObjectItem>.from(_objectPool)..shuffle(random);
    _targetObjects = shuffledPool.take(6).toList();
    
    // Add 4 distractor objects
    final remainingObjects = shuffledPool.skip(6).take(4).toList();
    _allObjects = [..._targetObjects, ...remainingObjects]..shuffle(random);
  }

  void _startMemorization() {
    setState(() {
      _currentPhase = ObjectMemoryPhase.memorization;
      _timeRemaining = 8; // 8 seconds to memorize
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });
      
      if (_timeRemaining <= 0) {
        timer.cancel();
        _startRecall();
      }
    });
  }

  void _startRecall() {
    setState(() {
      _currentPhase = ObjectMemoryPhase.recall;
    });
  }

  void _toggleObjectSelection(String objectId) {
    setState(() {
      if (_selectedObjects.contains(objectId)) {
        _selectedObjects.remove(objectId);
      } else {
        _selectedObjects.add(objectId);
      }
    });
  }

  void _submitAnswer() {
    int correctSelections = 0;
    for (String objectId in _selectedObjects) {
      if (_targetObjects.any((obj) => obj.id == objectId)) {
        correctSelections++;
      }
    }
    
    // Calculate score (correct selections - incorrect selections, minimum 0)
    int incorrectSelections = _selectedObjects.length - correctSelections;
    _score = (correctSelections - incorrectSelections).clamp(0, 6);
    
    setState(() {
      _currentPhase = ObjectMemoryPhase.results;
    });
  }

  void _restartExercise() {
    _generateObjects();
    setState(() {
      _currentPhase = ObjectMemoryPhase.instructions;
      _score = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Object Memory Recall',
          style: AppTheme.headlineSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.warningColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop, otherwise navigate to exercises
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              context.go(AppRouter.exercises);
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600.w),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case ObjectMemoryPhase.instructions:
        return _buildInstructionsPhase();
      case ObjectMemoryPhase.memorization:
        return _buildMemorizationPhase();
      case ObjectMemoryPhase.recall:
        return _buildRecallPhase();
      case ObjectMemoryPhase.results:
        return _buildResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.warningColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.inventory,
            size: 60.sp,
            color: AppTheme.warningColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Object Memory Recall',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                '1. You will see 6 everyday objects for 8 seconds\n'
                '2. Memorize all the objects carefully\n'
                '3. Then select the objects you remember\n'
                '4. Try to get all 6 correct!',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startMemorization,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.warningColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMemorizationPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.timer,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Memorize these objects: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Wrap(
            alignment: WrapAlignment.center,
            spacing: AppTheme.spacing12,
            runSpacing: AppTheme.spacing12,
            children: _targetObjects.map((object) {
              return Container(
                width: 100.w,
                height: 100.h,
                child: _buildObjectWidget(object),
              );
            }).toList(),
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Remember all the everyday objects!',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildRecallPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.warningColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.warningColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.quiz,
                color: AppTheme.warningColor,
                size: 32.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Select the objects you remember',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Tap on the objects you saw earlier (${_selectedObjects.length}/6 selected)',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        Expanded(
          child: SingleChildScrollView(
            child: Wrap(
              alignment: WrapAlignment.center,
              spacing: AppTheme.spacing8,
              runSpacing: AppTheme.spacing8,
              children: _allObjects.map((object) {
                final isSelected = _selectedObjects.contains(object.id);

                return GestureDetector(
                  onTap: () => _toggleObjectSelection(object.id),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: ResponsiveBreakpoints.isMobile(context) ? 140.w : 120.w,
                    height: ResponsiveBreakpoints.isMobile(context) ? 120.h : 100.h,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppTheme.warningColor.withOpacity(0.2)
                          : AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.warningColor
                            : AppTheme.dividerColor,
                        width: isSelected ? 3.w : 1.w,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: AppTheme.warningColor.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: _buildObjectWidget(object, isSelected: isSelected),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _selectedObjects.isNotEmpty ? _submitAnswer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.warningColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsPhase() {
    int correctSelections = 0;
    int incorrectSelections = 0;

    for (String objectId in _selectedObjects) {
      if (_targetObjects.any((obj) => obj.id == objectId)) {
        correctSelections++;
      } else {
        incorrectSelections++;
      }
    }

    int missedObjects = _targetObjects.length - correctSelections;
    double percentage = (correctSelections / _targetObjects.length) * 100;

    Color scoreColor;
    String scoreMessage;
    IconData scoreIcon;

    if (percentage >= 80) {
      scoreColor = AppTheme.successColor;
      scoreMessage = 'Excellent object memory!';
      scoreIcon = Icons.star;
    } else if (percentage >= 60) {
      scoreColor = AppTheme.accentColor;
      scoreMessage = 'Good visual recognition!';
      scoreIcon = Icons.thumb_up;
    } else {
      scoreColor = AppTheme.warningColor;
      scoreMessage = 'Keep practicing!';
      scoreIcon = Icons.trending_up;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            scoreIcon,
            size: 60.sp,
            color: scoreColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Exercise Complete!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: scoreColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              Divider(color: scoreColor.withOpacity(0.3)),
              SizedBox(height: AppTheme.spacing16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildScoreStat(
                    icon: Icons.check_circle,
                    label: 'Correct',
                    value: '$correctSelections',
                    color: AppTheme.successColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.cancel,
                    label: 'Incorrect',
                    value: '$incorrectSelections',
                    color: AppTheme.errorColor,
                  ),
                  _buildScoreStat(
                    icon: Icons.help_outline,
                    label: 'Missed',
                    value: '$missedObjects',
                    color: AppTheme.warningColor,
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _restartExercise,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.warningColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Try Again',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScoreStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24.sp,
        ),
        SizedBox(height: AppTheme.spacing4),
        ResponsiveText(
          value,
          style: AppTheme.titleLarge.copyWith(
            fontWeight: FontWeight.w700,
            color: color,
          ),
        ),
        ResponsiveText(
          label,
          style: AppTheme.labelSmall.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildObjectWidget(ObjectItem object, {bool isSelected = false}) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: object.color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            ),
            child: Icon(
              object.icon,
              color: isSelected ? AppTheme.warningColor : object.color,
              size: 24.sp,
            ),
          ),
          SizedBox(height: AppTheme.spacing4),
          Flexible(
            child: ResponsiveText(
              object.name,
              style: AppTheme.labelSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? AppTheme.warningColor
                    : AppTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

class ObjectItem {
  final String id;
  final String name;
  final IconData icon;
  final Color color;

  ObjectItem({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}
