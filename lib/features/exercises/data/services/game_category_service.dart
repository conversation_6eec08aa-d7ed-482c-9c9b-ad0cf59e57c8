import 'dart:convert';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';
import '../models/game_category_model.dart';

class GameCategoryService {
  static Future<List<GameCategoryModel>?> getGameCategories({
    String? accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/${ApiConfig.listGameCategory}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        if (accessToken != null) 'Authorization': 'Bearer $accessToken',
      };

      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        print('✅ Game categories loaded successfully: ${data.length} categories');
        return data.map((json) => GameCategoryModel.fromJson(json)).toList();
      } else {
        print(
          'Failed to load game categories. Status: ${response.statusCode}, Response: ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Error loading game categories: $e');
      return null;
    }
  }
}