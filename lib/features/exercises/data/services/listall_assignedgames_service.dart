import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/core/urls/api_config.dart';
import 'package:recallloop/features/exercises/data/models/assigned_game_model.dart';
import 'package:recallloop/features/exercises/data/models/game_config_model.dart';

class AssignedGamesInfoService {
  static final HiveUserService _hiveUserService = HiveUserService();
  static Future<List<AssignedGameInfo>?> getAssignedGames({
    // required String tenantId,
    required String tenantCode,
    required String partyId,
    required String partyTypeKey,
    String? accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.listAllAssignedGames}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        if (accessToken != null) 'Authorization': 'Bearer $accessToken',
      };

      final body = {
        // "tenant_id": tenantId,
        "tenant_code": tenantCode,
        "party_id": partyId,
        "party_type_key": partyTypeKey,
      };

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => AssignedGameInfo.fromJson(json)).toList();
      } else {
        print(
          'Failed to load assigned games. Status: ${response.statusCode}, Response: ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Error loading assigned games: $e');
      return null;
    }
  }

  static Future<GameConfig?> loadGameConfig(String gameId) async {
    try {
      final currentUser = await _hiveUserService.getCurrentUser();
      if (currentUser != null) {
        final gameConfig = await _getGame(
          gameId: gameId,
          accessToken: currentUser.accessToken,
        );
        return gameConfig;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<GameConfig?> _getGame({
    required String accessToken,
    required String gameId,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}${ApiConfig.getGameConfig}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        'Authorization': 'Bearer $accessToken',
      };

      final body = {"game_id": gameId};
      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData is Map<String, dynamic>) {
          return GameConfig.fromJson(responseData);
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<Map<String, dynamic>?> startGame({
    required String gameId,
    required String levelId,
  }) async {
    try {
      final currentUser = await _hiveUserService.getCurrentUser();
      if (currentUser != null) {
        final url = Uri.parse(
          '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.startGame}',
        );

        final headers = {
          'Content-Type': 'application/json',
          'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
          'Authorization': 'Bearer ${currentUser.accessToken}',
        };

        final body = {
          "tenant_id": currentUser.tenantId,
          "game_id": gameId,
          "level_id": levelId,
          "user_id": currentUser.partyId,
          "device_info": '1234567890', // Replace with actual device info
          "session_start_time": DateTime.now().toIso8601String(),
        };

        final response = await http.post(
          url,
          headers: headers,
          body: jsonEncode(body),
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return data;
        } else {
          print('Failed to start game: ${response.statusCode}');
          return null;
        }
      }
    } catch (e) {
      print(' Error starting game: $e');
      return null;
    }
    return null;
  }

  static Future<Map<String, dynamic>?> endGame({
    required String gameId,
    required String levelId,
    required String sessionId,
    required String score,
  }) async {
    try {
      final currentUser = await _hiveUserService.getCurrentUser();

      if (currentUser != null) {
        final url = Uri.parse(
          '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.endGame}',
        );

        final headers = {
          'Content-Type': 'application/json',
          'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
          'Authorization': 'Bearer ${currentUser.accessToken}',
        };

        final body = {
          "tenant_id": currentUser.tenantId,
          "game_id": gameId,
          "level_id": levelId,
          "user_id": currentUser.partyId,
          "session_end_time": DateTime.now().toIso8601String(),
          "session_id": sessionId,
          "score": score,
        };
        final response = await http.post(
          url,
          headers: headers,
          body: jsonEncode(body),
        );
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return data;
        } else {
          print('Failed to end game: ${response.statusCode}');
          return null;
        }
      }
    } catch (e) {
      print('Error ending game: $e');
      return null;
    }
    return null;
  }

  static Future<Map<String, dynamic>?> getActiveSession(String gameId) async {
    try {
      final currentUser = await _hiveUserService.getCurrentUser();
      if (currentUser != null) {
        final url = Uri.parse(
          '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.getActiveSession}',
        );

        final headers = {
          'Content-Type': 'application/json',
          'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
          'Authorization': 'Bearer ${currentUser.accessToken}',
        };

        final body = {"user_id": currentUser.partyId, "game_id": gameId};

        final response = await http.post(
          url,
          headers: headers,
          body: jsonEncode(body),
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          return data;
        }
      }
    } catch (e) {
      print(' Error getting active session: $e');
      return null;
    }
    return null;
  }
}
