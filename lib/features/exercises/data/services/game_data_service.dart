import 'package:recallloop/core/constants/game_constants.dart';

class GameDataService {
  static GameDataService? _instance;
  static GameDataService get instance => _instance ??= GameDataService._();
  GameDataService._();

  Map<String, dynamic>? _gameData;

  Future<void> loadGameData() async {
    if (_gameData != null) return;
    
    // Use constants instead of loading from JSON
    _gameData = {
      'word_recall': GameConstants.wordRecallData,
      'shape_pattern': {'patterns': GameConstants.shapePatternData},
      'letter_pattern': {'patterns': GameConstants.letterPatternData},
      'number_pattern': {'patterns': GameConstants.numberPatternData},
    };
  }

  // Word Recall methods
  List<String> getWords(String difficulty) {
    return List<String>.from(_gameData!['word_recall']?[difficulty] ?? []);
  }

  List<List<String>> getShapeSequences() {
    final patterns = _gameData!['shape_pattern']?['patterns'] as List<dynamic>?;
    print(patterns);
    print('patterns: $patterns');
    if (patterns == null) {
      return [];
    }
    return patterns.map((pattern) {
      return List<String>.from(pattern['sequence'] as List<dynamic>);
    }).toList();
  }

  List<Map<String, dynamic>> getShapePatterns() {
    final patterns = _gameData!['shape_pattern']?['patterns'] as List<dynamic>?;
    if (patterns == null) {
      return [];
    }
    return patterns
        .map((pattern) => Map<String, dynamic>.from(pattern))
        .toList();
  }

  List<Map<String, dynamic>> getLetterPatterns() {
    final patterns =
        _gameData!['letter_pattern']?['patterns'] as List<dynamic>?;
    if (patterns == null) {
      return [];
    }
    return patterns
        .map((pattern) => Map<String, dynamic>.from(pattern))
        .toList();
  }

  List<Map<String, dynamic>> getNumberPatterns() {
    final patterns =
        _gameData!['number_pattern']?['patterns'] as List<dynamic>?;
    if (patterns == null) {
      return [];
    }
    return patterns
        .map((pattern) => Map<String, dynamic>.from(pattern))
        .toList();
  }

  // Visual Memory methods
  List<String> getVisualColors() {
    return List<String>.from(_gameData!['visual_memory']['colors'] ?? []);
  }

  // List<Map<String, dynamic>> getShapePatterns() {
  //   final patterns = _gameData?['shape_pattern']?['patterns'];
  //   if (patterns is List) {
  //     return patterns.map((e) => Map<String, dynamic>.from(e)).toList();
  //   }
  //   return [];
  // }

  int getGridSize(String difficulty) {
    return _gameData!['visual_memory']['grid_sizes'][difficulty] ?? 3;
  }

  int getItemCount(String difficulty) {
    return _gameData!['visual_memory']['item_counts'][difficulty] ?? 3;
  }

  // Pattern Recognition methods
  List<List<int>> getColorPatterns() {
    return (_gameData!['pattern_recognition']['color_patterns'] as List)
        .map((e) => List<int>.from(e))
        .toList();
  }

  List<String> getPatternColors() {
    return List<String>.from(_gameData!['pattern_recognition']['colors'] ?? []);
  }

  List<String> getPatternShapes() {
    return List<String>.from(_gameData!['pattern_recognition']['shapes'] ?? []);
  }

  List<List<double>> getSizePatterns() {
    return (_gameData!['pattern_recognition']['size_patterns'] as List)
        .map((e) => List<double>.from(e))
        .toList();
  }

  // Sequence Memory methods
  List<String> getButtonColors() {
    return List<String>.from(
      _gameData!['sequence_memory']['button_colors'] ?? [],
    );
  }

  int getSequenceLength(String difficulty) {
    return _gameData!['sequence_memory']['sequence_lengths'][difficulty] ?? 4;
  }
}
