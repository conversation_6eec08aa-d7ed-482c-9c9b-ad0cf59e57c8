// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'game_category_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

GameCategoryModel _$GameCategoryModelFromJson(Map<String, dynamic> json) {
  return _GameCategoryModel.fromJson(json);
}

/// @nodoc
mixin _$GameCategoryModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String? get iconName => throw _privateConstructorUsedError;
  String? get colorCode => throw _privateConstructorUsedError;

  /// Serializes this GameCategoryModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of GameCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GameCategoryModelCopyWith<GameCategoryModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GameCategoryModelCopyWith<$Res> {
  factory $GameCategoryModelCopyWith(
    GameCategoryModel value,
    $Res Function(GameCategoryModel) then,
  ) = _$GameCategoryModelCopyWithImpl<$Res, GameCategoryModel>;
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String? iconName,
    String? colorCode,
  });
}

/// @nodoc
class _$GameCategoryModelCopyWithImpl<$Res, $Val extends GameCategoryModel>
    implements $GameCategoryModelCopyWith<$Res> {
  _$GameCategoryModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of GameCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? iconName = freezed,
    Object? colorCode = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            description: null == description
                ? _value.description
                : description // ignore: cast_nullable_to_non_nullable
                      as String,
            iconName: freezed == iconName
                ? _value.iconName
                : iconName // ignore: cast_nullable_to_non_nullable
                      as String?,
            colorCode: freezed == colorCode
                ? _value.colorCode
                : colorCode // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$GameCategoryModelImplCopyWith<$Res>
    implements $GameCategoryModelCopyWith<$Res> {
  factory _$$GameCategoryModelImplCopyWith(
    _$GameCategoryModelImpl value,
    $Res Function(_$GameCategoryModelImpl) then,
  ) = __$$GameCategoryModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String description,
    String? iconName,
    String? colorCode,
  });
}

/// @nodoc
class __$$GameCategoryModelImplCopyWithImpl<$Res>
    extends _$GameCategoryModelCopyWithImpl<$Res, _$GameCategoryModelImpl>
    implements _$$GameCategoryModelImplCopyWith<$Res> {
  __$$GameCategoryModelImplCopyWithImpl(
    _$GameCategoryModelImpl _value,
    $Res Function(_$GameCategoryModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of GameCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? iconName = freezed,
    Object? colorCode = freezed,
  }) {
    return _then(
      _$GameCategoryModelImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _value.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        iconName: freezed == iconName
            ? _value.iconName
            : iconName // ignore: cast_nullable_to_non_nullable
                  as String?,
        colorCode: freezed == colorCode
            ? _value.colorCode
            : colorCode // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$GameCategoryModelImpl implements _GameCategoryModel {
  const _$GameCategoryModelImpl({
    required this.id,
    required this.name,
    required this.description,
    this.iconName,
    this.colorCode,
  });

  factory _$GameCategoryModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$GameCategoryModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String description;
  @override
  final String? iconName;
  @override
  final String? colorCode;

  @override
  String toString() {
    return 'GameCategoryModel(id: $id, name: $name, description: $description, iconName: $iconName, colorCode: $colorCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GameCategoryModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.iconName, iconName) ||
                other.iconName == iconName) &&
            (identical(other.colorCode, colorCode) ||
                other.colorCode == colorCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, description, iconName, colorCode);

  /// Create a copy of GameCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GameCategoryModelImplCopyWith<_$GameCategoryModelImpl> get copyWith =>
      __$$GameCategoryModelImplCopyWithImpl<_$GameCategoryModelImpl>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$$GameCategoryModelImplToJson(this);
  }
}

abstract class _GameCategoryModel implements GameCategoryModel {
  const factory _GameCategoryModel({
    required final String id,
    required final String name,
    required final String description,
    final String? iconName,
    final String? colorCode,
  }) = _$GameCategoryModelImpl;

  factory _GameCategoryModel.fromJson(Map<String, dynamic> json) =
      _$GameCategoryModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get description;
  @override
  String? get iconName;
  @override
  String? get colorCode;

  /// Create a copy of GameCategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GameCategoryModelImplCopyWith<_$GameCategoryModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
