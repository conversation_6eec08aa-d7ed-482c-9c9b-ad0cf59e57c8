class GameConfig {
  final String id;
  final String title;
  final String category;
  final int level;
  final int? timer;
  final String instructions;
  final GameAssets? assets;
  final GameLogic? logic;
  final GameRewards rewards;
  final int? targetScore;
  final String? levelId;
  final String? patternType;

  GameConfig({
    required this.id,
    required this.title,
    required this.category,
    required this.level,
    this.timer,
    required this.instructions,
    required this.assets,
    required this.logic,
    required this.rewards,
    this.levelId,
    this.targetScore,
    this.patternType,
  });

  factory GameConfig.fromJson(Map<String, dynamic> json) {
    return GameConfig(
      id: json['id'],
      title: json['title'],
      category: json['category'],
      level: json['level'],
      timer: json['timer'],
      instructions: json['instructions'],
      assets: json['assets'] != null
          ? GameAssets.fromJson(json['assets'])
          : null,
      logic: json['logic'] != null ? GameLogic.fromJson(json['logic']) : null,

      rewards: GameRewards.fromJson(json['rewards']),
      targetScore: json['target_score'],
      levelId: json['level_id'],
      patternType: json['pattern_type'] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'category': category,
    'level': level,
    'timer': timer,
    'instructions': instructions,
    'assets': assets?.toJson(),
    'logic': logic?.toJson(),
    'rewards': rewards.toJson(),
    'target_score': targetScore,
    'level_id': levelId,
    'pattern_type': patternType,
  };
}

class GameAssets {
  final List<String>? words;
  final List<Map<String, dynamic>>? problems;
  final List<Map<String, dynamic>>? patterns;
  final List<Map<String, dynamic>>? targetShapes;
  final List<Map<String, dynamic>>? distractorShapes;
  final String? gridSize;
  final List<String>? shapes;
  final List<String>? colors;
  final List<List<int>>? positions;
  final int? sequenceLength;
  final List<int>? sequence;
  final int? memorizationTime;

  GameAssets({
    this.words,
    this.problems,
    this.patterns,
    this.targetShapes,
    this.distractorShapes,
    this.gridSize,
    this.shapes,
    this.colors,
    this.positions,
    this.sequenceLength,
    this.sequence,
    this.memorizationTime,
  });

  factory GameAssets.fromJson(Map<String, dynamic>? json) {
    if (json == null) return GameAssets();
    return GameAssets(
      words: (json['words'] as List?)?.cast<String>(),
      problems: (json['problems'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      patterns: (json['patterns'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      targetShapes: (json['target_shapes'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      distractorShapes: (json['distractor_shapes'] as List?)
          ?.map((e) => Map<String, dynamic>.from(e))
          .toList(),
      gridSize: json['grid_size'],
      shapes: (json['shapes'] as List?)?.cast<String>(),
      colors: (json['colors'] as List?)?.cast<String>(),
      positions: (json['positions'] as List?)
          ?.map((pos) => List<int>.from(pos))
          .toList(),
      sequenceLength: json['sequence_length'],
      sequence: (json['sequence'] as List?)?.cast<int>(),
      memorizationTime: json['memorization_time'],
    );
  }

  Map<String, dynamic> toJson() => {
    'words': words,
    'problems': problems,
    'patterns': patterns,
    'target_shapes': targetShapes,
    'distractor_shapes': distractorShapes,
    'grid_size': gridSize,
    'shapes': shapes,
    'colors': colors,
    'positions': positions,
    'sequence_length': sequenceLength,
    'sequence': sequence,
    'memorization_time': memorizationTime,
  };
}

class GameLogic {
  final int minCorrect;
  final dynamic nextGameUnlocked; // to handle both bool and null

  GameLogic({required this.minCorrect, required this.nextGameUnlocked});

  factory GameLogic.fromJson(Map<String, dynamic>? json) {
    if (json == null) return GameLogic(minCorrect: 0, nextGameUnlocked: false);
    return GameLogic(
      minCorrect: json['min_correct'],
      nextGameUnlocked: json['next_game_unlocked'],
    );
  }

  Map<String, dynamic> toJson() => {
    'min_correct': minCorrect,
    'next_game_unlocked': nextGameUnlocked,
  };
}

class GameRewards {
  final int points;
  final String? badge;
  final String? badgeId;
  final String? badgeName;

  GameRewards({required this.points, this.badge, this.badgeId, this.badgeName});

  factory GameRewards.fromJson(Map<String, dynamic> json) {
    return GameRewards(
      points: json['points'],
      badge: json['badge'],
      badgeId: json['badge_id'],
      badgeName: json['badge_name'],
    );
  }

  Map<String, dynamic> toJson() => {
    'points': points,
    'badge': badge,
    'badge_id': badgeId,
    'badge_name': badgeName,
  };
}
