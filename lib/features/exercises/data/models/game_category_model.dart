import 'package:freezed_annotation/freezed_annotation.dart';

part 'game_category_model.freezed.dart';
part 'game_category_model.g.dart';

@freezed
class GameCategoryModel with _$GameCategoryModel {
  const factory GameCategoryModel({
    required String id,
    required String name,
    required String description,
    String? iconName,
    String? colorCode,
  }) = _GameCategoryModel;

  factory GameCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$GameCategoryModelFromJson(json);
}
