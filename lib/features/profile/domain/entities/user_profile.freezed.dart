// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$UserProfile {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  int get age => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  ExerciseStats get exerciseStats => throw _privateConstructorUsedError;
  List<MoodEntry> get recentMoods => throw _privateConstructorUsedError;
  GameSummaryStats? get gameSummaryStats => throw _privateConstructorUsedError;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserProfileCopyWith<UserProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileCopyWith<$Res> {
  factory $UserProfileCopyWith(
    UserProfile value,
    $Res Function(UserProfile) then,
  ) = _$UserProfileCopyWithImpl<$Res, UserProfile>;
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    int age,
    String? avatarUrl,
    DateTime createdAt,
    ExerciseStats exerciseStats,
    List<MoodEntry> recentMoods,
    GameSummaryStats? gameSummaryStats,
  });

  $ExerciseStatsCopyWith<$Res> get exerciseStats;
}

/// @nodoc
class _$UserProfileCopyWithImpl<$Res, $Val extends UserProfile>
    implements $UserProfileCopyWith<$Res> {
  _$UserProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? age = null,
    Object? avatarUrl = freezed,
    Object? createdAt = null,
    Object? exerciseStats = null,
    Object? recentMoods = null,
    Object? gameSummaryStats = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            name: null == name
                ? _value.name
                : name // ignore: cast_nullable_to_non_nullable
                      as String,
            email: null == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String,
            age: null == age
                ? _value.age
                : age // ignore: cast_nullable_to_non_nullable
                      as int,
            avatarUrl: freezed == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: null == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            exerciseStats: null == exerciseStats
                ? _value.exerciseStats
                : exerciseStats // ignore: cast_nullable_to_non_nullable
                      as ExerciseStats,
            recentMoods: null == recentMoods
                ? _value.recentMoods
                : recentMoods // ignore: cast_nullable_to_non_nullable
                      as List<MoodEntry>,
            gameSummaryStats: freezed == gameSummaryStats
                ? _value.gameSummaryStats
                : gameSummaryStats // ignore: cast_nullable_to_non_nullable
                      as GameSummaryStats?,
          )
          as $Val,
    );
  }

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ExerciseStatsCopyWith<$Res> get exerciseStats {
    return $ExerciseStatsCopyWith<$Res>(_value.exerciseStats, (value) {
      return _then(_value.copyWith(exerciseStats: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserProfileImplCopyWith<$Res>
    implements $UserProfileCopyWith<$Res> {
  factory _$$UserProfileImplCopyWith(
    _$UserProfileImpl value,
    $Res Function(_$UserProfileImpl) then,
  ) = __$$UserProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    String name,
    String email,
    int age,
    String? avatarUrl,
    DateTime createdAt,
    ExerciseStats exerciseStats,
    List<MoodEntry> recentMoods,
    GameSummaryStats? gameSummaryStats,
  });

  @override
  $ExerciseStatsCopyWith<$Res> get exerciseStats;
}

/// @nodoc
class __$$UserProfileImplCopyWithImpl<$Res>
    extends _$UserProfileCopyWithImpl<$Res, _$UserProfileImpl>
    implements _$$UserProfileImplCopyWith<$Res> {
  __$$UserProfileImplCopyWithImpl(
    _$UserProfileImpl _value,
    $Res Function(_$UserProfileImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? age = null,
    Object? avatarUrl = freezed,
    Object? createdAt = null,
    Object? exerciseStats = null,
    Object? recentMoods = null,
    Object? gameSummaryStats = freezed,
  }) {
    return _then(
      _$UserProfileImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _value.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        email: null == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String,
        age: null == age
            ? _value.age
            : age // ignore: cast_nullable_to_non_nullable
                  as int,
        avatarUrl: freezed == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: null == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        exerciseStats: null == exerciseStats
            ? _value.exerciseStats
            : exerciseStats // ignore: cast_nullable_to_non_nullable
                  as ExerciseStats,
        recentMoods: null == recentMoods
            ? _value._recentMoods
            : recentMoods // ignore: cast_nullable_to_non_nullable
                  as List<MoodEntry>,
        gameSummaryStats: freezed == gameSummaryStats
            ? _value.gameSummaryStats
            : gameSummaryStats // ignore: cast_nullable_to_non_nullable
                  as GameSummaryStats?,
      ),
    );
  }
}

/// @nodoc

class _$UserProfileImpl implements _UserProfile {
  const _$UserProfileImpl({
    required this.id,
    required this.name,
    required this.email,
    required this.age,
    this.avatarUrl,
    required this.createdAt,
    required this.exerciseStats,
    required final List<MoodEntry> recentMoods,
    this.gameSummaryStats,
  }) : _recentMoods = recentMoods;

  @override
  final String id;
  @override
  final String name;
  @override
  final String email;
  @override
  final int age;
  @override
  final String? avatarUrl;
  @override
  final DateTime createdAt;
  @override
  final ExerciseStats exerciseStats;
  final List<MoodEntry> _recentMoods;
  @override
  List<MoodEntry> get recentMoods {
    if (_recentMoods is EqualUnmodifiableListView) return _recentMoods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentMoods);
  }

  @override
  final GameSummaryStats? gameSummaryStats;

  @override
  String toString() {
    return 'UserProfile(id: $id, name: $name, email: $email, age: $age, avatarUrl: $avatarUrl, createdAt: $createdAt, exerciseStats: $exerciseStats, recentMoods: $recentMoods, gameSummaryStats: $gameSummaryStats)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.exerciseStats, exerciseStats) ||
                other.exerciseStats == exerciseStats) &&
            const DeepCollectionEquality().equals(
              other._recentMoods,
              _recentMoods,
            ) &&
            (identical(other.gameSummaryStats, gameSummaryStats) ||
                other.gameSummaryStats == gameSummaryStats));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    email,
    age,
    avatarUrl,
    createdAt,
    exerciseStats,
    const DeepCollectionEquality().hash(_recentMoods),
    gameSummaryStats,
  );

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      __$$UserProfileImplCopyWithImpl<_$UserProfileImpl>(this, _$identity);
}

abstract class _UserProfile implements UserProfile {
  const factory _UserProfile({
    required final String id,
    required final String name,
    required final String email,
    required final int age,
    final String? avatarUrl,
    required final DateTime createdAt,
    required final ExerciseStats exerciseStats,
    required final List<MoodEntry> recentMoods,
    final GameSummaryStats? gameSummaryStats,
  }) = _$UserProfileImpl;

  @override
  String get id;
  @override
  String get name;
  @override
  String get email;
  @override
  int get age;
  @override
  String? get avatarUrl;
  @override
  DateTime get createdAt;
  @override
  ExerciseStats get exerciseStats;
  @override
  List<MoodEntry> get recentMoods;
  @override
  GameSummaryStats? get gameSummaryStats;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ExerciseStats {
  int get totalExercises => throw _privateConstructorUsedError;
  int get memoryMatchingScore => throw _privateConstructorUsedError;
  int get wordRecallScore => throw _privateConstructorUsedError;
  int get averageScore => throw _privateConstructorUsedError;
  int get streak => throw _privateConstructorUsedError;

  /// Create a copy of ExerciseStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ExerciseStatsCopyWith<ExerciseStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExerciseStatsCopyWith<$Res> {
  factory $ExerciseStatsCopyWith(
    ExerciseStats value,
    $Res Function(ExerciseStats) then,
  ) = _$ExerciseStatsCopyWithImpl<$Res, ExerciseStats>;
  @useResult
  $Res call({
    int totalExercises,
    int memoryMatchingScore,
    int wordRecallScore,
    int averageScore,
    int streak,
  });
}

/// @nodoc
class _$ExerciseStatsCopyWithImpl<$Res, $Val extends ExerciseStats>
    implements $ExerciseStatsCopyWith<$Res> {
  _$ExerciseStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExerciseStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalExercises = null,
    Object? memoryMatchingScore = null,
    Object? wordRecallScore = null,
    Object? averageScore = null,
    Object? streak = null,
  }) {
    return _then(
      _value.copyWith(
            totalExercises: null == totalExercises
                ? _value.totalExercises
                : totalExercises // ignore: cast_nullable_to_non_nullable
                      as int,
            memoryMatchingScore: null == memoryMatchingScore
                ? _value.memoryMatchingScore
                : memoryMatchingScore // ignore: cast_nullable_to_non_nullable
                      as int,
            wordRecallScore: null == wordRecallScore
                ? _value.wordRecallScore
                : wordRecallScore // ignore: cast_nullable_to_non_nullable
                      as int,
            averageScore: null == averageScore
                ? _value.averageScore
                : averageScore // ignore: cast_nullable_to_non_nullable
                      as int,
            streak: null == streak
                ? _value.streak
                : streak // ignore: cast_nullable_to_non_nullable
                      as int,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$ExerciseStatsImplCopyWith<$Res>
    implements $ExerciseStatsCopyWith<$Res> {
  factory _$$ExerciseStatsImplCopyWith(
    _$ExerciseStatsImpl value,
    $Res Function(_$ExerciseStatsImpl) then,
  ) = __$$ExerciseStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int totalExercises,
    int memoryMatchingScore,
    int wordRecallScore,
    int averageScore,
    int streak,
  });
}

/// @nodoc
class __$$ExerciseStatsImplCopyWithImpl<$Res>
    extends _$ExerciseStatsCopyWithImpl<$Res, _$ExerciseStatsImpl>
    implements _$$ExerciseStatsImplCopyWith<$Res> {
  __$$ExerciseStatsImplCopyWithImpl(
    _$ExerciseStatsImpl _value,
    $Res Function(_$ExerciseStatsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of ExerciseStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalExercises = null,
    Object? memoryMatchingScore = null,
    Object? wordRecallScore = null,
    Object? averageScore = null,
    Object? streak = null,
  }) {
    return _then(
      _$ExerciseStatsImpl(
        totalExercises: null == totalExercises
            ? _value.totalExercises
            : totalExercises // ignore: cast_nullable_to_non_nullable
                  as int,
        memoryMatchingScore: null == memoryMatchingScore
            ? _value.memoryMatchingScore
            : memoryMatchingScore // ignore: cast_nullable_to_non_nullable
                  as int,
        wordRecallScore: null == wordRecallScore
            ? _value.wordRecallScore
            : wordRecallScore // ignore: cast_nullable_to_non_nullable
                  as int,
        averageScore: null == averageScore
            ? _value.averageScore
            : averageScore // ignore: cast_nullable_to_non_nullable
                  as int,
        streak: null == streak
            ? _value.streak
            : streak // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc

class _$ExerciseStatsImpl implements _ExerciseStats {
  const _$ExerciseStatsImpl({
    required this.totalExercises,
    required this.memoryMatchingScore,
    required this.wordRecallScore,
    required this.averageScore,
    required this.streak,
  });

  @override
  final int totalExercises;
  @override
  final int memoryMatchingScore;
  @override
  final int wordRecallScore;
  @override
  final int averageScore;
  @override
  final int streak;

  @override
  String toString() {
    return 'ExerciseStats(totalExercises: $totalExercises, memoryMatchingScore: $memoryMatchingScore, wordRecallScore: $wordRecallScore, averageScore: $averageScore, streak: $streak)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExerciseStatsImpl &&
            (identical(other.totalExercises, totalExercises) ||
                other.totalExercises == totalExercises) &&
            (identical(other.memoryMatchingScore, memoryMatchingScore) ||
                other.memoryMatchingScore == memoryMatchingScore) &&
            (identical(other.wordRecallScore, wordRecallScore) ||
                other.wordRecallScore == wordRecallScore) &&
            (identical(other.averageScore, averageScore) ||
                other.averageScore == averageScore) &&
            (identical(other.streak, streak) || other.streak == streak));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    totalExercises,
    memoryMatchingScore,
    wordRecallScore,
    averageScore,
    streak,
  );

  /// Create a copy of ExerciseStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExerciseStatsImplCopyWith<_$ExerciseStatsImpl> get copyWith =>
      __$$ExerciseStatsImplCopyWithImpl<_$ExerciseStatsImpl>(this, _$identity);
}

abstract class _ExerciseStats implements ExerciseStats {
  const factory _ExerciseStats({
    required final int totalExercises,
    required final int memoryMatchingScore,
    required final int wordRecallScore,
    required final int averageScore,
    required final int streak,
  }) = _$ExerciseStatsImpl;

  @override
  int get totalExercises;
  @override
  int get memoryMatchingScore;
  @override
  int get wordRecallScore;
  @override
  int get averageScore;
  @override
  int get streak;

  /// Create a copy of ExerciseStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExerciseStatsImplCopyWith<_$ExerciseStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$MoodEntry {
  String get id => throw _privateConstructorUsedError;
  int get moodLevel => throw _privateConstructorUsedError; // 1-5 scale
  String? get note => throw _privateConstructorUsedError;
  DateTime get timestamp => throw _privateConstructorUsedError;
  List<String> get tags => throw _privateConstructorUsedError;
  String? get activityBefore => throw _privateConstructorUsedError;

  /// Create a copy of MoodEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MoodEntryCopyWith<MoodEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoodEntryCopyWith<$Res> {
  factory $MoodEntryCopyWith(MoodEntry value, $Res Function(MoodEntry) then) =
      _$MoodEntryCopyWithImpl<$Res, MoodEntry>;
  @useResult
  $Res call({
    String id,
    int moodLevel,
    String? note,
    DateTime timestamp,
    List<String> tags,
    String? activityBefore,
  });
}

/// @nodoc
class _$MoodEntryCopyWithImpl<$Res, $Val extends MoodEntry>
    implements $MoodEntryCopyWith<$Res> {
  _$MoodEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MoodEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? moodLevel = null,
    Object? note = freezed,
    Object? timestamp = null,
    Object? tags = null,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            moodLevel: null == moodLevel
                ? _value.moodLevel
                : moodLevel // ignore: cast_nullable_to_non_nullable
                      as int,
            note: freezed == note
                ? _value.note
                : note // ignore: cast_nullable_to_non_nullable
                      as String?,
            timestamp: null == timestamp
                ? _value.timestamp
                : timestamp // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            tags: null == tags
                ? _value.tags
                : tags // ignore: cast_nullable_to_non_nullable
                      as List<String>,
            activityBefore: freezed == activityBefore
                ? _value.activityBefore
                : activityBefore // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$MoodEntryImplCopyWith<$Res>
    implements $MoodEntryCopyWith<$Res> {
  factory _$$MoodEntryImplCopyWith(
    _$MoodEntryImpl value,
    $Res Function(_$MoodEntryImpl) then,
  ) = __$$MoodEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    String id,
    int moodLevel,
    String? note,
    DateTime timestamp,
    List<String> tags,
    String? activityBefore,
  });
}

/// @nodoc
class __$$MoodEntryImplCopyWithImpl<$Res>
    extends _$MoodEntryCopyWithImpl<$Res, _$MoodEntryImpl>
    implements _$$MoodEntryImplCopyWith<$Res> {
  __$$MoodEntryImplCopyWithImpl(
    _$MoodEntryImpl _value,
    $Res Function(_$MoodEntryImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of MoodEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? moodLevel = null,
    Object? note = freezed,
    Object? timestamp = null,
    Object? tags = null,
    Object? activityBefore = freezed,
  }) {
    return _then(
      _$MoodEntryImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        moodLevel: null == moodLevel
            ? _value.moodLevel
            : moodLevel // ignore: cast_nullable_to_non_nullable
                  as int,
        note: freezed == note
            ? _value.note
            : note // ignore: cast_nullable_to_non_nullable
                  as String?,
        timestamp: null == timestamp
            ? _value.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        tags: null == tags
            ? _value._tags
            : tags // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        activityBefore: freezed == activityBefore
            ? _value.activityBefore
            : activityBefore // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$MoodEntryImpl implements _MoodEntry {
  const _$MoodEntryImpl({
    required this.id,
    required this.moodLevel,
    this.note,
    required this.timestamp,
    final List<String> tags = const [],
    this.activityBefore,
  }) : _tags = tags;

  @override
  final String id;
  @override
  final int moodLevel;
  // 1-5 scale
  @override
  final String? note;
  @override
  final DateTime timestamp;
  final List<String> _tags;
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final String? activityBefore;

  @override
  String toString() {
    return 'MoodEntry(id: $id, moodLevel: $moodLevel, note: $note, timestamp: $timestamp, tags: $tags, activityBefore: $activityBefore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoodEntryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.moodLevel, moodLevel) ||
                other.moodLevel == moodLevel) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.activityBefore, activityBefore) ||
                other.activityBefore == activityBefore));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    moodLevel,
    note,
    timestamp,
    const DeepCollectionEquality().hash(_tags),
    activityBefore,
  );

  /// Create a copy of MoodEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MoodEntryImplCopyWith<_$MoodEntryImpl> get copyWith =>
      __$$MoodEntryImplCopyWithImpl<_$MoodEntryImpl>(this, _$identity);
}

abstract class _MoodEntry implements MoodEntry {
  const factory _MoodEntry({
    required final String id,
    required final int moodLevel,
    final String? note,
    required final DateTime timestamp,
    final List<String> tags,
    final String? activityBefore,
  }) = _$MoodEntryImpl;

  @override
  String get id;
  @override
  int get moodLevel; // 1-5 scale
  @override
  String? get note;
  @override
  DateTime get timestamp;
  @override
  List<String> get tags;
  @override
  String? get activityBefore;

  /// Create a copy of MoodEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MoodEntryImplCopyWith<_$MoodEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
