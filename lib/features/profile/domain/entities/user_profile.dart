import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:recallloop/features/home/<USER>/models/game_summary_stat_model.dart';

// import 'package:recallloop/features/profile/domain/entities/game_summary_stats.dart';

part 'user_profile.freezed.dart';

@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String name,
    required String email,
    required int age,
    String? avatarUrl,
    required DateTime createdAt,
    required ExerciseStats exerciseStats,
    required List<MoodEntry> recentMoods,
    GameSummaryStats? gameSummaryStats,
  }) = _UserProfile;
}

@freezed
class ExerciseStats with _$ExerciseStats {
  const factory ExerciseStats({
    required int totalExercises,
    required int memoryMatchingScore,
    required int wordRecallScore,
    required int averageScore,
    required int streak,
  }) = _ExerciseStats;
}

@freezed
class MoodEntry with _$MoodEntry {
  const factory MoodEntry({
    required String id,
    required int moodLevel, // 1-5 scale
    String? note,
    required DateTime timestamp,
    @Default([]) List<String> tags,
    String? activityBefore,
  }) = _MoodEntry;
}
