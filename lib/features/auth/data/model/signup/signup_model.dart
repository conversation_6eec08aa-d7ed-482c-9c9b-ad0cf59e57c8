import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_model.freezed.dart';
part 'signup_model.g.dart';

@freezed
class SignupModel with _$SignupModel {
  const factory SignupModel({
    @J<PERSON><PERSON>ey(name: "access_token") required String accessToken,
    @J<PERSON><PERSON><PERSON>(name: "token_type") String? tokenType,
    @Json<PERSON><PERSON>(name: "expires_in") int? expiresIn,
    @<PERSON>son<PERSON><PERSON>(name: "expires_at") int? expiresAt,
    @<PERSON>son<PERSON><PERSON>(name: "refresh_token") String? refreshToken,
    @<PERSON><PERSON><PERSON><PERSON>(name: "user") User? user,
  }) = _SignupModel;

  factory SignupModel.fromJson(Map<String, dynamic> json) =>
      _$SignupModelFromJson(json);
}

@freezed
class User with _$User {
  const factory User({
    @Json<PERSON>ey(name: "id") required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: "aud") String? aud,
    @<PERSON><PERSON><PERSON><PERSON>(name: "role") String? role,
    @<PERSON><PERSON><PERSON><PERSON>(name: "email") String? email,
    @<PERSON><PERSON><PERSON><PERSON>(name: "email_confirmed_at") String? emailConfirmedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: "phone") String? phone,
    @Json<PERSON>ey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata? appMetadata,
    @JsonKey(name: "user_metadata") Data? userMetadata,
    @JsonKey(name: "identities") List<Identity>? identities,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "is_anonymous") bool? isAnonymous,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class AppMetadata with _$AppMetadata {
  const factory AppMetadata({
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "providers") List<String>? providers,
  }) = _AppMetadata;

  factory AppMetadata.fromJson(Map<String, dynamic> json) =>
      _$AppMetadataFromJson(json);
}

@freezed
class Identity with _$Identity {
  const factory Identity({
    @JsonKey(name: "identity_id") String? identityId,
    @JsonKey(name: "id") String? id,
    @JsonKey(name: "user_id") String? userId,
    @JsonKey(name: "identity_data") Data? identityData,
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "email") String? email,
  }) = _Identity;

  factory Identity.fromJson(Map<String, dynamic> json) =>
      _$IdentityFromJson(json);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}
