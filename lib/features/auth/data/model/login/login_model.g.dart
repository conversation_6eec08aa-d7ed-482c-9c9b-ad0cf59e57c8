// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LoginModelImpl _$$LoginModelImplFromJson(Map<String, dynamic> json) =>
    _$LoginModelImpl(
      accessToken: json['access_token'] as String,
      tokenType: json['token_type'] as String?,
      expiresIn: (json['expires_in'] as num?)?.toInt(),
      expiresAt: (json['expires_at'] as num?)?.toInt(),
      refreshToken: json['refresh_token'] as String?,
      user: json['user'] == null
          ? null
          : User.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$LoginModelImplToJson(_$LoginModelImpl instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'token_type': instance.tokenType,
      'expires_in': instance.expiresIn,
      'expires_at': instance.expiresAt,
      'refresh_token': instance.refreshToken,
      'user': instance.user,
    };

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
  id: json['id'] as String,
  aud: json['aud'] as String?,
  role: json['role'] as String?,
  email: json['email'] as String?,
  emailConfirmedAt: json['email_confirmed_at'] as String?,
  phone: json['phone'] as String?,
  confirmedAt: json['confirmed_at'] as String?,
  lastSignInAt: json['last_sign_in_at'] as String?,
  appMetadata: json['app_metadata'] == null
      ? null
      : AppMetadata.fromJson(json['app_metadata'] as Map<String, dynamic>),
  userMetadata: json['user_metadata'] == null
      ? null
      : UserMetadata.fromJson(json['user_metadata'] as Map<String, dynamic>),
  identities: (json['identities'] as List<dynamic>?)
      ?.map((e) => Identity.fromJson(e as Map<String, dynamic>))
      .toList(),
  createdAt: json['created_at'] as String?,
  updatedAt: json['updated_at'] as String?,
  isAnonymous: json['is_anonymous'] as bool?,
);

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'aud': instance.aud,
      'role': instance.role,
      'email': instance.email,
      'email_confirmed_at': instance.emailConfirmedAt,
      'phone': instance.phone,
      'confirmed_at': instance.confirmedAt,
      'last_sign_in_at': instance.lastSignInAt,
      'app_metadata': instance.appMetadata,
      'user_metadata': instance.userMetadata,
      'identities': instance.identities,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'is_anonymous': instance.isAnonymous,
    };

_$AppMetadataImpl _$$AppMetadataImplFromJson(Map<String, dynamic> json) =>
    _$AppMetadataImpl(
      provider: json['provider'] as String?,
      providers: (json['providers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$$AppMetadataImplToJson(_$AppMetadataImpl instance) =>
    <String, dynamic>{
      'provider': instance.provider,
      'providers': instance.providers,
    };

_$IdentityImpl _$$IdentityImplFromJson(Map<String, dynamic> json) =>
    _$IdentityImpl(
      identityId: json['identity_id'] as String?,
      id: json['id'] as String?,
      userId: json['user_id'] as String?,
      identityData: json['identity_data'] == null
          ? null
          : IdentityData.fromJson(
              json['identity_data'] as Map<String, dynamic>,
            ),
      provider: json['provider'] as String?,
      lastSignInAt: json['last_sign_in_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$$IdentityImplToJson(_$IdentityImpl instance) =>
    <String, dynamic>{
      'identity_id': instance.identityId,
      'id': instance.id,
      'user_id': instance.userId,
      'identity_data': instance.identityData,
      'provider': instance.provider,
      'last_sign_in_at': instance.lastSignInAt,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'email': instance.email,
    };

_$IdentityDataImpl _$$IdentityDataImplFromJson(Map<String, dynamic> json) =>
    _$IdentityDataImpl(
      address: json['address'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      email: json['email'] as String?,
      emailVerified: json['email_verified'] as bool?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      partyId: json['party_id'] as String?,
      partyTypeKey: json['party_type_key'] as String?,
      phone: json['phone'] as String?,
      phoneVerified: json['phone_verified'] as bool?,
      sub: json['sub'] as String?,
      tenantCode: json['tenant_code'] as String?,
    );

Map<String, dynamic> _$$IdentityDataImplToJson(_$IdentityDataImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'avatar_url': instance.avatarUrl,
      'email': instance.email,
      'email_verified': instance.emailVerified,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'party_id': instance.partyId,
      'party_type_key': instance.partyTypeKey,
      'phone': instance.phone,
      'phone_verified': instance.phoneVerified,
      'sub': instance.sub,
      'tenant_code': instance.tenantCode,
    };

_$UserMetadataImpl _$$UserMetadataImplFromJson(Map<String, dynamic> json) =>
    _$UserMetadataImpl(
      address: json['address'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      email: json['email'] as String?,
      emailVerified: json['email_verified'] as bool?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      partyId: json['party_id'] as String?,
      partyTypeKey: json['party_type_key'] as String?,
      phone: json['phone'] as String?,
      phoneVerified: json['phone_verified'] as bool?,
      sub: json['sub'] as String?,
      tenantCode: json['tenant_code'] as String?,
      tenantId: json['tenant_id'] as String?,
    );

Map<String, dynamic> _$$UserMetadataImplToJson(_$UserMetadataImpl instance) =>
    <String, dynamic>{
      'address': instance.address,
      'avatar_url': instance.avatarUrl,
      'email': instance.email,
      'email_verified': instance.emailVerified,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'party_id': instance.partyId,
      'party_type_key': instance.partyTypeKey,
      'phone': instance.phone,
      'phone_verified': instance.phoneVerified,
      'sub': instance.sub,
      'tenant_code': instance.tenantCode,
      'tenant_id': instance.tenantId,
    };
