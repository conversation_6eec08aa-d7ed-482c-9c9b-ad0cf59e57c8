import 'package:freezed_annotation/freezed_annotation.dart';

part 'login_model.freezed.dart';
part 'login_model.g.dart';

@freezed
class LoginModel with _$LoginModel {
  const factory LoginModel({
    @Json<PERSON>ey(name: "access_token") required String accessToken,
    @Json<PERSON><PERSON>(name: "token_type") String? tokenType,
    @Json<PERSON>ey(name: "expires_in") int? expiresIn,
    @Json<PERSON><PERSON>(name: "expires_at") int? expiresAt,
    @Json<PERSON>ey(name: "refresh_token") String? refreshToken,
    @<PERSON>son<PERSON>ey(name: "user") User? user,
  }) = _LoginModel;

  factory LoginModel.fromJson(Map<String, dynamic> json) =>
      _$LoginModelFromJson(json);

  static LoginModel fromSup<PERSON>se<PERSON><PERSON>(dynamic user) {
    // TODO: Implement conversion from Supabase user to LoginModel
    throw UnimplementedError('fromSupabaseUser is not implemented');
  }
}

@freezed
class User with _$User {
  const factory User({
    @Json<PERSON><PERSON>(name: "id") required String id,
    @J<PERSON><PERSON><PERSON>(name: "aud") String? aud,
    @<PERSON><PERSON><PERSON><PERSON>(name: "role") String? role,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_confirmed_at") String? emailConfirmedAt,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "confirmed_at") String? confirmedAt,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata? appMetadata,
    @JsonKey(name: "user_metadata") UserMetadata? userMetadata,
    @JsonKey(name: "identities") List<Identity>? identities,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "is_anonymous") bool? isAnonymous,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class AppMetadata with _$AppMetadata {
  const factory AppMetadata({
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "providers") List<String>? providers,
  }) = _AppMetadata;

  factory AppMetadata.fromJson(Map<String, dynamic> json) =>
      _$AppMetadataFromJson(json);
}

@freezed
class Identity with _$Identity {
  const factory Identity({
    @JsonKey(name: "identity_id") String? identityId,
    @JsonKey(name: "id") String? id,
    @JsonKey(name: "user_id") String? userId,
    @JsonKey(name: "identity_data") IdentityData? identityData,
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "email") String? email,
  }) = _Identity;

  factory Identity.fromJson(Map<String, dynamic> json) =>
      _$IdentityFromJson(json);
}

@freezed
class IdentityData with _$IdentityData {
  const factory IdentityData({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
  }) = _IdentityData;

  factory IdentityData.fromJson(Map<String, dynamic> json) =>
      _$IdentityDataFromJson(json);
}

@freezed
class UserMetadata with _$UserMetadata {
  const factory UserMetadata({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
    @JsonKey(name: "tenant_id") String? tenantId,
  }) = _UserMetadata;

  factory UserMetadata.fromJson(Map<String, dynamic> json) =>
      _$UserMetadataFromJson(json);
}
