// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

LoginModel _$LoginModelFromJson(Map<String, dynamic> json) {
  return _LoginModel.fromJson(json);
}

/// @nodoc
mixin _$LoginModel {
  @JsonKey(name: "access_token")
  String get accessToken => throw _privateConstructorUsedError;
  @JsonKey(name: "token_type")
  String? get tokenType => throw _privateConstructorUsedError;
  @JsonKey(name: "expires_in")
  int? get expiresIn => throw _privateConstructorUsedError;
  @JsonKey(name: "expires_at")
  int? get expiresAt => throw _privateConstructorUsedError;
  @JsonKey(name: "refresh_token")
  String? get refreshToken => throw _privateConstructorUsedError;
  @JsonKey(name: "user")
  User? get user => throw _privateConstructorUsedError;

  /// Serializes this LoginModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginModelCopyWith<LoginModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginModelCopyWith<$Res> {
  factory $LoginModelCopyWith(
    LoginModel value,
    $Res Function(LoginModel) then,
  ) = _$LoginModelCopyWithImpl<$Res, LoginModel>;
  @useResult
  $Res call({
    @JsonKey(name: "access_token") String accessToken,
    @JsonKey(name: "token_type") String? tokenType,
    @JsonKey(name: "expires_in") int? expiresIn,
    @JsonKey(name: "expires_at") int? expiresAt,
    @JsonKey(name: "refresh_token") String? refreshToken,
    @JsonKey(name: "user") User? user,
  });

  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class _$LoginModelCopyWithImpl<$Res, $Val extends LoginModel>
    implements $LoginModelCopyWith<$Res> {
  _$LoginModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? tokenType = freezed,
    Object? expiresIn = freezed,
    Object? expiresAt = freezed,
    Object? refreshToken = freezed,
    Object? user = freezed,
  }) {
    return _then(
      _value.copyWith(
            accessToken: null == accessToken
                ? _value.accessToken
                : accessToken // ignore: cast_nullable_to_non_nullable
                      as String,
            tokenType: freezed == tokenType
                ? _value.tokenType
                : tokenType // ignore: cast_nullable_to_non_nullable
                      as String?,
            expiresIn: freezed == expiresIn
                ? _value.expiresIn
                : expiresIn // ignore: cast_nullable_to_non_nullable
                      as int?,
            expiresAt: freezed == expiresAt
                ? _value.expiresAt
                : expiresAt // ignore: cast_nullable_to_non_nullable
                      as int?,
            refreshToken: freezed == refreshToken
                ? _value.refreshToken
                : refreshToken // ignore: cast_nullable_to_non_nullable
                      as String?,
            user: freezed == user
                ? _value.user
                : user // ignore: cast_nullable_to_non_nullable
                      as User?,
          )
          as $Val,
    );
  }

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res>? get user {
    if (_value.user == null) {
      return null;
    }

    return $UserCopyWith<$Res>(_value.user!, (value) {
      return _then(_value.copyWith(user: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LoginModelImplCopyWith<$Res>
    implements $LoginModelCopyWith<$Res> {
  factory _$$LoginModelImplCopyWith(
    _$LoginModelImpl value,
    $Res Function(_$LoginModelImpl) then,
  ) = __$$LoginModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "access_token") String accessToken,
    @JsonKey(name: "token_type") String? tokenType,
    @JsonKey(name: "expires_in") int? expiresIn,
    @JsonKey(name: "expires_at") int? expiresAt,
    @JsonKey(name: "refresh_token") String? refreshToken,
    @JsonKey(name: "user") User? user,
  });

  @override
  $UserCopyWith<$Res>? get user;
}

/// @nodoc
class __$$LoginModelImplCopyWithImpl<$Res>
    extends _$LoginModelCopyWithImpl<$Res, _$LoginModelImpl>
    implements _$$LoginModelImplCopyWith<$Res> {
  __$$LoginModelImplCopyWithImpl(
    _$LoginModelImpl _value,
    $Res Function(_$LoginModelImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? tokenType = freezed,
    Object? expiresIn = freezed,
    Object? expiresAt = freezed,
    Object? refreshToken = freezed,
    Object? user = freezed,
  }) {
    return _then(
      _$LoginModelImpl(
        accessToken: null == accessToken
            ? _value.accessToken
            : accessToken // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenType: freezed == tokenType
            ? _value.tokenType
            : tokenType // ignore: cast_nullable_to_non_nullable
                  as String?,
        expiresIn: freezed == expiresIn
            ? _value.expiresIn
            : expiresIn // ignore: cast_nullable_to_non_nullable
                  as int?,
        expiresAt: freezed == expiresAt
            ? _value.expiresAt
            : expiresAt // ignore: cast_nullable_to_non_nullable
                  as int?,
        refreshToken: freezed == refreshToken
            ? _value.refreshToken
            : refreshToken // ignore: cast_nullable_to_non_nullable
                  as String?,
        user: freezed == user
            ? _value.user
            : user // ignore: cast_nullable_to_non_nullable
                  as User?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$LoginModelImpl implements _LoginModel {
  const _$LoginModelImpl({
    @JsonKey(name: "access_token") required this.accessToken,
    @JsonKey(name: "token_type") this.tokenType,
    @JsonKey(name: "expires_in") this.expiresIn,
    @JsonKey(name: "expires_at") this.expiresAt,
    @JsonKey(name: "refresh_token") this.refreshToken,
    @JsonKey(name: "user") this.user,
  });

  factory _$LoginModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$LoginModelImplFromJson(json);

  @override
  @JsonKey(name: "access_token")
  final String accessToken;
  @override
  @JsonKey(name: "token_type")
  final String? tokenType;
  @override
  @JsonKey(name: "expires_in")
  final int? expiresIn;
  @override
  @JsonKey(name: "expires_at")
  final int? expiresAt;
  @override
  @JsonKey(name: "refresh_token")
  final String? refreshToken;
  @override
  @JsonKey(name: "user")
  final User? user;

  @override
  String toString() {
    return 'LoginModel(accessToken: $accessToken, tokenType: $tokenType, expiresIn: $expiresIn, expiresAt: $expiresAt, refreshToken: $refreshToken, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginModelImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    accessToken,
    tokenType,
    expiresIn,
    expiresAt,
    refreshToken,
    user,
  );

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginModelImplCopyWith<_$LoginModelImpl> get copyWith =>
      __$$LoginModelImplCopyWithImpl<_$LoginModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LoginModelImplToJson(this);
  }
}

abstract class _LoginModel implements LoginModel {
  const factory _LoginModel({
    @JsonKey(name: "access_token") required final String accessToken,
    @JsonKey(name: "token_type") final String? tokenType,
    @JsonKey(name: "expires_in") final int? expiresIn,
    @JsonKey(name: "expires_at") final int? expiresAt,
    @JsonKey(name: "refresh_token") final String? refreshToken,
    @JsonKey(name: "user") final User? user,
  }) = _$LoginModelImpl;

  factory _LoginModel.fromJson(Map<String, dynamic> json) =
      _$LoginModelImpl.fromJson;

  @override
  @JsonKey(name: "access_token")
  String get accessToken;
  @override
  @JsonKey(name: "token_type")
  String? get tokenType;
  @override
  @JsonKey(name: "expires_in")
  int? get expiresIn;
  @override
  @JsonKey(name: "expires_at")
  int? get expiresAt;
  @override
  @JsonKey(name: "refresh_token")
  String? get refreshToken;
  @override
  @JsonKey(name: "user")
  User? get user;

  /// Create a copy of LoginModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginModelImplCopyWith<_$LoginModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  @JsonKey(name: "id")
  String get id => throw _privateConstructorUsedError;
  @JsonKey(name: "aud")
  String? get aud => throw _privateConstructorUsedError;
  @JsonKey(name: "role")
  String? get role => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: "email_confirmed_at")
  String? get emailConfirmedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "confirmed_at")
  String? get confirmedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "last_sign_in_at")
  String? get lastSignInAt => throw _privateConstructorUsedError;
  @JsonKey(name: "app_metadata")
  AppMetadata? get appMetadata => throw _privateConstructorUsedError;
  @JsonKey(name: "user_metadata")
  UserMetadata? get userMetadata => throw _privateConstructorUsedError;
  @JsonKey(name: "identities")
  List<Identity>? get identities => throw _privateConstructorUsedError;
  @JsonKey(name: "created_at")
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  String? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "is_anonymous")
  bool? get isAnonymous => throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call({
    @JsonKey(name: "id") String id,
    @JsonKey(name: "aud") String? aud,
    @JsonKey(name: "role") String? role,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_confirmed_at") String? emailConfirmedAt,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "confirmed_at") String? confirmedAt,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata? appMetadata,
    @JsonKey(name: "user_metadata") UserMetadata? userMetadata,
    @JsonKey(name: "identities") List<Identity>? identities,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "is_anonymous") bool? isAnonymous,
  });

  $AppMetadataCopyWith<$Res>? get appMetadata;
  $UserMetadataCopyWith<$Res>? get userMetadata;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? aud = freezed,
    Object? role = freezed,
    Object? email = freezed,
    Object? emailConfirmedAt = freezed,
    Object? phone = freezed,
    Object? confirmedAt = freezed,
    Object? lastSignInAt = freezed,
    Object? appMetadata = freezed,
    Object? userMetadata = freezed,
    Object? identities = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isAnonymous = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String,
            aud: freezed == aud
                ? _value.aud
                : aud // ignore: cast_nullable_to_non_nullable
                      as String?,
            role: freezed == role
                ? _value.role
                : role // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            emailConfirmedAt: freezed == emailConfirmedAt
                ? _value.emailConfirmedAt
                : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            phone: freezed == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String?,
            confirmedAt: freezed == confirmedAt
                ? _value.confirmedAt
                : confirmedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastSignInAt: freezed == lastSignInAt
                ? _value.lastSignInAt
                : lastSignInAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            appMetadata: freezed == appMetadata
                ? _value.appMetadata
                : appMetadata // ignore: cast_nullable_to_non_nullable
                      as AppMetadata?,
            userMetadata: freezed == userMetadata
                ? _value.userMetadata
                : userMetadata // ignore: cast_nullable_to_non_nullable
                      as UserMetadata?,
            identities: freezed == identities
                ? _value.identities
                : identities // ignore: cast_nullable_to_non_nullable
                      as List<Identity>?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            isAnonymous: freezed == isAnonymous
                ? _value.isAnonymous
                : isAnonymous // ignore: cast_nullable_to_non_nullable
                      as bool?,
          )
          as $Val,
    );
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppMetadataCopyWith<$Res>? get appMetadata {
    if (_value.appMetadata == null) {
      return null;
    }

    return $AppMetadataCopyWith<$Res>(_value.appMetadata!, (value) {
      return _then(_value.copyWith(appMetadata: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserMetadataCopyWith<$Res>? get userMetadata {
    if (_value.userMetadata == null) {
      return null;
    }

    return $UserMetadataCopyWith<$Res>(_value.userMetadata!, (value) {
      return _then(_value.copyWith(userMetadata: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
    _$UserImpl value,
    $Res Function(_$UserImpl) then,
  ) = __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "id") String id,
    @JsonKey(name: "aud") String? aud,
    @JsonKey(name: "role") String? role,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_confirmed_at") String? emailConfirmedAt,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "confirmed_at") String? confirmedAt,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "app_metadata") AppMetadata? appMetadata,
    @JsonKey(name: "user_metadata") UserMetadata? userMetadata,
    @JsonKey(name: "identities") List<Identity>? identities,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "is_anonymous") bool? isAnonymous,
  });

  @override
  $AppMetadataCopyWith<$Res>? get appMetadata;
  @override
  $UserMetadataCopyWith<$Res>? get userMetadata;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
    : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? aud = freezed,
    Object? role = freezed,
    Object? email = freezed,
    Object? emailConfirmedAt = freezed,
    Object? phone = freezed,
    Object? confirmedAt = freezed,
    Object? lastSignInAt = freezed,
    Object? appMetadata = freezed,
    Object? userMetadata = freezed,
    Object? identities = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? isAnonymous = freezed,
  }) {
    return _then(
      _$UserImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        aud: freezed == aud
            ? _value.aud
            : aud // ignore: cast_nullable_to_non_nullable
                  as String?,
        role: freezed == role
            ? _value.role
            : role // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        emailConfirmedAt: freezed == emailConfirmedAt
            ? _value.emailConfirmedAt
            : emailConfirmedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        phone: freezed == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String?,
        confirmedAt: freezed == confirmedAt
            ? _value.confirmedAt
            : confirmedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastSignInAt: freezed == lastSignInAt
            ? _value.lastSignInAt
            : lastSignInAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        appMetadata: freezed == appMetadata
            ? _value.appMetadata
            : appMetadata // ignore: cast_nullable_to_non_nullable
                  as AppMetadata?,
        userMetadata: freezed == userMetadata
            ? _value.userMetadata
            : userMetadata // ignore: cast_nullable_to_non_nullable
                  as UserMetadata?,
        identities: freezed == identities
            ? _value._identities
            : identities // ignore: cast_nullable_to_non_nullable
                  as List<Identity>?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        isAnonymous: freezed == isAnonymous
            ? _value.isAnonymous
            : isAnonymous // ignore: cast_nullable_to_non_nullable
                  as bool?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl({
    @JsonKey(name: "id") required this.id,
    @JsonKey(name: "aud") this.aud,
    @JsonKey(name: "role") this.role,
    @JsonKey(name: "email") this.email,
    @JsonKey(name: "email_confirmed_at") this.emailConfirmedAt,
    @JsonKey(name: "phone") this.phone,
    @JsonKey(name: "confirmed_at") this.confirmedAt,
    @JsonKey(name: "last_sign_in_at") this.lastSignInAt,
    @JsonKey(name: "app_metadata") this.appMetadata,
    @JsonKey(name: "user_metadata") this.userMetadata,
    @JsonKey(name: "identities") final List<Identity>? identities,
    @JsonKey(name: "created_at") this.createdAt,
    @JsonKey(name: "updated_at") this.updatedAt,
    @JsonKey(name: "is_anonymous") this.isAnonymous,
  }) : _identities = identities;

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  @JsonKey(name: "id")
  final String id;
  @override
  @JsonKey(name: "aud")
  final String? aud;
  @override
  @JsonKey(name: "role")
  final String? role;
  @override
  @JsonKey(name: "email")
  final String? email;
  @override
  @JsonKey(name: "email_confirmed_at")
  final String? emailConfirmedAt;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "confirmed_at")
  final String? confirmedAt;
  @override
  @JsonKey(name: "last_sign_in_at")
  final String? lastSignInAt;
  @override
  @JsonKey(name: "app_metadata")
  final AppMetadata? appMetadata;
  @override
  @JsonKey(name: "user_metadata")
  final UserMetadata? userMetadata;
  final List<Identity>? _identities;
  @override
  @JsonKey(name: "identities")
  List<Identity>? get identities {
    final value = _identities;
    if (value == null) return null;
    if (_identities is EqualUnmodifiableListView) return _identities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @JsonKey(name: "created_at")
  final String? createdAt;
  @override
  @JsonKey(name: "updated_at")
  final String? updatedAt;
  @override
  @JsonKey(name: "is_anonymous")
  final bool? isAnonymous;

  @override
  String toString() {
    return 'User(id: $id, aud: $aud, role: $role, email: $email, emailConfirmedAt: $emailConfirmedAt, phone: $phone, confirmedAt: $confirmedAt, lastSignInAt: $lastSignInAt, appMetadata: $appMetadata, userMetadata: $userMetadata, identities: $identities, createdAt: $createdAt, updatedAt: $updatedAt, isAnonymous: $isAnonymous)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.aud, aud) || other.aud == aud) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailConfirmedAt, emailConfirmedAt) ||
                other.emailConfirmedAt == emailConfirmedAt) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.confirmedAt, confirmedAt) ||
                other.confirmedAt == confirmedAt) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.appMetadata, appMetadata) ||
                other.appMetadata == appMetadata) &&
            (identical(other.userMetadata, userMetadata) ||
                other.userMetadata == userMetadata) &&
            const DeepCollectionEquality().equals(
              other._identities,
              _identities,
            ) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.isAnonymous, isAnonymous) ||
                other.isAnonymous == isAnonymous));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    aud,
    role,
    email,
    emailConfirmedAt,
    phone,
    confirmedAt,
    lastSignInAt,
    appMetadata,
    userMetadata,
    const DeepCollectionEquality().hash(_identities),
    createdAt,
    updatedAt,
    isAnonymous,
  );

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(this);
  }
}

abstract class _User implements User {
  const factory _User({
    @JsonKey(name: "id") required final String id,
    @JsonKey(name: "aud") final String? aud,
    @JsonKey(name: "role") final String? role,
    @JsonKey(name: "email") final String? email,
    @JsonKey(name: "email_confirmed_at") final String? emailConfirmedAt,
    @JsonKey(name: "phone") final String? phone,
    @JsonKey(name: "confirmed_at") final String? confirmedAt,
    @JsonKey(name: "last_sign_in_at") final String? lastSignInAt,
    @JsonKey(name: "app_metadata") final AppMetadata? appMetadata,
    @JsonKey(name: "user_metadata") final UserMetadata? userMetadata,
    @JsonKey(name: "identities") final List<Identity>? identities,
    @JsonKey(name: "created_at") final String? createdAt,
    @JsonKey(name: "updated_at") final String? updatedAt,
    @JsonKey(name: "is_anonymous") final bool? isAnonymous,
  }) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  @JsonKey(name: "id")
  String get id;
  @override
  @JsonKey(name: "aud")
  String? get aud;
  @override
  @JsonKey(name: "role")
  String? get role;
  @override
  @JsonKey(name: "email")
  String? get email;
  @override
  @JsonKey(name: "email_confirmed_at")
  String? get emailConfirmedAt;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "confirmed_at")
  String? get confirmedAt;
  @override
  @JsonKey(name: "last_sign_in_at")
  String? get lastSignInAt;
  @override
  @JsonKey(name: "app_metadata")
  AppMetadata? get appMetadata;
  @override
  @JsonKey(name: "user_metadata")
  UserMetadata? get userMetadata;
  @override
  @JsonKey(name: "identities")
  List<Identity>? get identities;
  @override
  @JsonKey(name: "created_at")
  String? get createdAt;
  @override
  @JsonKey(name: "updated_at")
  String? get updatedAt;
  @override
  @JsonKey(name: "is_anonymous")
  bool? get isAnonymous;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AppMetadata _$AppMetadataFromJson(Map<String, dynamic> json) {
  return _AppMetadata.fromJson(json);
}

/// @nodoc
mixin _$AppMetadata {
  @JsonKey(name: "provider")
  String? get provider => throw _privateConstructorUsedError;
  @JsonKey(name: "providers")
  List<String>? get providers => throw _privateConstructorUsedError;

  /// Serializes this AppMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppMetadataCopyWith<AppMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppMetadataCopyWith<$Res> {
  factory $AppMetadataCopyWith(
    AppMetadata value,
    $Res Function(AppMetadata) then,
  ) = _$AppMetadataCopyWithImpl<$Res, AppMetadata>;
  @useResult
  $Res call({
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "providers") List<String>? providers,
  });
}

/// @nodoc
class _$AppMetadataCopyWithImpl<$Res, $Val extends AppMetadata>
    implements $AppMetadataCopyWith<$Res> {
  _$AppMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? provider = freezed, Object? providers = freezed}) {
    return _then(
      _value.copyWith(
            provider: freezed == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as String?,
            providers: freezed == providers
                ? _value.providers
                : providers // ignore: cast_nullable_to_non_nullable
                      as List<String>?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AppMetadataImplCopyWith<$Res>
    implements $AppMetadataCopyWith<$Res> {
  factory _$$AppMetadataImplCopyWith(
    _$AppMetadataImpl value,
    $Res Function(_$AppMetadataImpl) then,
  ) = __$$AppMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "providers") List<String>? providers,
  });
}

/// @nodoc
class __$$AppMetadataImplCopyWithImpl<$Res>
    extends _$AppMetadataCopyWithImpl<$Res, _$AppMetadataImpl>
    implements _$$AppMetadataImplCopyWith<$Res> {
  __$$AppMetadataImplCopyWithImpl(
    _$AppMetadataImpl _value,
    $Res Function(_$AppMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? provider = freezed, Object? providers = freezed}) {
    return _then(
      _$AppMetadataImpl(
        provider: freezed == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as String?,
        providers: freezed == providers
            ? _value._providers
            : providers // ignore: cast_nullable_to_non_nullable
                  as List<String>?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AppMetadataImpl implements _AppMetadata {
  const _$AppMetadataImpl({
    @JsonKey(name: "provider") this.provider,
    @JsonKey(name: "providers") final List<String>? providers,
  }) : _providers = providers;

  factory _$AppMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppMetadataImplFromJson(json);

  @override
  @JsonKey(name: "provider")
  final String? provider;
  final List<String>? _providers;
  @override
  @JsonKey(name: "providers")
  List<String>? get providers {
    final value = _providers;
    if (value == null) return null;
    if (_providers is EqualUnmodifiableListView) return _providers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AppMetadata(provider: $provider, providers: $providers)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppMetadataImpl &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            const DeepCollectionEquality().equals(
              other._providers,
              _providers,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    provider,
    const DeepCollectionEquality().hash(_providers),
  );

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppMetadataImplCopyWith<_$AppMetadataImpl> get copyWith =>
      __$$AppMetadataImplCopyWithImpl<_$AppMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppMetadataImplToJson(this);
  }
}

abstract class _AppMetadata implements AppMetadata {
  const factory _AppMetadata({
    @JsonKey(name: "provider") final String? provider,
    @JsonKey(name: "providers") final List<String>? providers,
  }) = _$AppMetadataImpl;

  factory _AppMetadata.fromJson(Map<String, dynamic> json) =
      _$AppMetadataImpl.fromJson;

  @override
  @JsonKey(name: "provider")
  String? get provider;
  @override
  @JsonKey(name: "providers")
  List<String>? get providers;

  /// Create a copy of AppMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppMetadataImplCopyWith<_$AppMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Identity _$IdentityFromJson(Map<String, dynamic> json) {
  return _Identity.fromJson(json);
}

/// @nodoc
mixin _$Identity {
  @JsonKey(name: "identity_id")
  String? get identityId => throw _privateConstructorUsedError;
  @JsonKey(name: "id")
  String? get id => throw _privateConstructorUsedError;
  @JsonKey(name: "user_id")
  String? get userId => throw _privateConstructorUsedError;
  @JsonKey(name: "identity_data")
  IdentityData? get identityData => throw _privateConstructorUsedError;
  @JsonKey(name: "provider")
  String? get provider => throw _privateConstructorUsedError;
  @JsonKey(name: "last_sign_in_at")
  String? get lastSignInAt => throw _privateConstructorUsedError;
  @JsonKey(name: "created_at")
  String? get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: "updated_at")
  String? get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String? get email => throw _privateConstructorUsedError;

  /// Serializes this Identity to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityCopyWith<Identity> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityCopyWith<$Res> {
  factory $IdentityCopyWith(Identity value, $Res Function(Identity) then) =
      _$IdentityCopyWithImpl<$Res, Identity>;
  @useResult
  $Res call({
    @JsonKey(name: "identity_id") String? identityId,
    @JsonKey(name: "id") String? id,
    @JsonKey(name: "user_id") String? userId,
    @JsonKey(name: "identity_data") IdentityData? identityData,
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "email") String? email,
  });

  $IdentityDataCopyWith<$Res>? get identityData;
}

/// @nodoc
class _$IdentityCopyWithImpl<$Res, $Val extends Identity>
    implements $IdentityCopyWith<$Res> {
  _$IdentityCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityId = freezed,
    Object? id = freezed,
    Object? userId = freezed,
    Object? identityData = freezed,
    Object? provider = freezed,
    Object? lastSignInAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? email = freezed,
  }) {
    return _then(
      _value.copyWith(
            identityId: freezed == identityId
                ? _value.identityId
                : identityId // ignore: cast_nullable_to_non_nullable
                      as String?,
            id: freezed == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as String?,
            userId: freezed == userId
                ? _value.userId
                : userId // ignore: cast_nullable_to_non_nullable
                      as String?,
            identityData: freezed == identityData
                ? _value.identityData
                : identityData // ignore: cast_nullable_to_non_nullable
                      as IdentityData?,
            provider: freezed == provider
                ? _value.provider
                : provider // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastSignInAt: freezed == lastSignInAt
                ? _value.lastSignInAt
                : lastSignInAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            createdAt: freezed == createdAt
                ? _value.createdAt
                : createdAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            updatedAt: freezed == updatedAt
                ? _value.updatedAt
                : updatedAt // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IdentityDataCopyWith<$Res>? get identityData {
    if (_value.identityData == null) {
      return null;
    }

    return $IdentityDataCopyWith<$Res>(_value.identityData!, (value) {
      return _then(_value.copyWith(identityData: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdentityImplCopyWith<$Res>
    implements $IdentityCopyWith<$Res> {
  factory _$$IdentityImplCopyWith(
    _$IdentityImpl value,
    $Res Function(_$IdentityImpl) then,
  ) = __$$IdentityImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "identity_id") String? identityId,
    @JsonKey(name: "id") String? id,
    @JsonKey(name: "user_id") String? userId,
    @JsonKey(name: "identity_data") IdentityData? identityData,
    @JsonKey(name: "provider") String? provider,
    @JsonKey(name: "last_sign_in_at") String? lastSignInAt,
    @JsonKey(name: "created_at") String? createdAt,
    @JsonKey(name: "updated_at") String? updatedAt,
    @JsonKey(name: "email") String? email,
  });

  @override
  $IdentityDataCopyWith<$Res>? get identityData;
}

/// @nodoc
class __$$IdentityImplCopyWithImpl<$Res>
    extends _$IdentityCopyWithImpl<$Res, _$IdentityImpl>
    implements _$$IdentityImplCopyWith<$Res> {
  __$$IdentityImplCopyWithImpl(
    _$IdentityImpl _value,
    $Res Function(_$IdentityImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityId = freezed,
    Object? id = freezed,
    Object? userId = freezed,
    Object? identityData = freezed,
    Object? provider = freezed,
    Object? lastSignInAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? email = freezed,
  }) {
    return _then(
      _$IdentityImpl(
        identityId: freezed == identityId
            ? _value.identityId
            : identityId // ignore: cast_nullable_to_non_nullable
                  as String?,
        id: freezed == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String?,
        userId: freezed == userId
            ? _value.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as String?,
        identityData: freezed == identityData
            ? _value.identityData
            : identityData // ignore: cast_nullable_to_non_nullable
                  as IdentityData?,
        provider: freezed == provider
            ? _value.provider
            : provider // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastSignInAt: freezed == lastSignInAt
            ? _value.lastSignInAt
            : lastSignInAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        createdAt: freezed == createdAt
            ? _value.createdAt
            : createdAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        updatedAt: freezed == updatedAt
            ? _value.updatedAt
            : updatedAt // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentityImpl implements _Identity {
  const _$IdentityImpl({
    @JsonKey(name: "identity_id") this.identityId,
    @JsonKey(name: "id") this.id,
    @JsonKey(name: "user_id") this.userId,
    @JsonKey(name: "identity_data") this.identityData,
    @JsonKey(name: "provider") this.provider,
    @JsonKey(name: "last_sign_in_at") this.lastSignInAt,
    @JsonKey(name: "created_at") this.createdAt,
    @JsonKey(name: "updated_at") this.updatedAt,
    @JsonKey(name: "email") this.email,
  });

  factory _$IdentityImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentityImplFromJson(json);

  @override
  @JsonKey(name: "identity_id")
  final String? identityId;
  @override
  @JsonKey(name: "id")
  final String? id;
  @override
  @JsonKey(name: "user_id")
  final String? userId;
  @override
  @JsonKey(name: "identity_data")
  final IdentityData? identityData;
  @override
  @JsonKey(name: "provider")
  final String? provider;
  @override
  @JsonKey(name: "last_sign_in_at")
  final String? lastSignInAt;
  @override
  @JsonKey(name: "created_at")
  final String? createdAt;
  @override
  @JsonKey(name: "updated_at")
  final String? updatedAt;
  @override
  @JsonKey(name: "email")
  final String? email;

  @override
  String toString() {
    return 'Identity(identityId: $identityId, id: $id, userId: $userId, identityData: $identityData, provider: $provider, lastSignInAt: $lastSignInAt, createdAt: $createdAt, updatedAt: $updatedAt, email: $email)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityImpl &&
            (identical(other.identityId, identityId) ||
                other.identityId == identityId) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.identityData, identityData) ||
                other.identityData == identityData) &&
            (identical(other.provider, provider) ||
                other.provider == provider) &&
            (identical(other.lastSignInAt, lastSignInAt) ||
                other.lastSignInAt == lastSignInAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.email, email) || other.email == email));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    identityId,
    id,
    userId,
    identityData,
    provider,
    lastSignInAt,
    createdAt,
    updatedAt,
    email,
  );

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityImplCopyWith<_$IdentityImpl> get copyWith =>
      __$$IdentityImplCopyWithImpl<_$IdentityImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentityImplToJson(this);
  }
}

abstract class _Identity implements Identity {
  const factory _Identity({
    @JsonKey(name: "identity_id") final String? identityId,
    @JsonKey(name: "id") final String? id,
    @JsonKey(name: "user_id") final String? userId,
    @JsonKey(name: "identity_data") final IdentityData? identityData,
    @JsonKey(name: "provider") final String? provider,
    @JsonKey(name: "last_sign_in_at") final String? lastSignInAt,
    @JsonKey(name: "created_at") final String? createdAt,
    @JsonKey(name: "updated_at") final String? updatedAt,
    @JsonKey(name: "email") final String? email,
  }) = _$IdentityImpl;

  factory _Identity.fromJson(Map<String, dynamic> json) =
      _$IdentityImpl.fromJson;

  @override
  @JsonKey(name: "identity_id")
  String? get identityId;
  @override
  @JsonKey(name: "id")
  String? get id;
  @override
  @JsonKey(name: "user_id")
  String? get userId;
  @override
  @JsonKey(name: "identity_data")
  IdentityData? get identityData;
  @override
  @JsonKey(name: "provider")
  String? get provider;
  @override
  @JsonKey(name: "last_sign_in_at")
  String? get lastSignInAt;
  @override
  @JsonKey(name: "created_at")
  String? get createdAt;
  @override
  @JsonKey(name: "updated_at")
  String? get updatedAt;
  @override
  @JsonKey(name: "email")
  String? get email;

  /// Create a copy of Identity
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityImplCopyWith<_$IdentityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

IdentityData _$IdentityDataFromJson(Map<String, dynamic> json) {
  return _IdentityData.fromJson(json);
}

/// @nodoc
mixin _$IdentityData {
  @JsonKey(name: "address")
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: "avatar_url")
  String? get avatarUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: "email_verified")
  bool? get emailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "first_name")
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: "last_name")
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: "party_id")
  String? get partyId => throw _privateConstructorUsedError;
  @JsonKey(name: "party_type_key")
  String? get partyTypeKey => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "phone_verified")
  bool? get phoneVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "sub")
  String? get sub => throw _privateConstructorUsedError;
  @JsonKey(name: "tenant_code")
  String? get tenantCode => throw _privateConstructorUsedError;

  /// Serializes this IdentityData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IdentityData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityDataCopyWith<IdentityData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityDataCopyWith<$Res> {
  factory $IdentityDataCopyWith(
    IdentityData value,
    $Res Function(IdentityData) then,
  ) = _$IdentityDataCopyWithImpl<$Res, IdentityData>;
  @useResult
  $Res call({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
  });
}

/// @nodoc
class _$IdentityDataCopyWithImpl<$Res, $Val extends IdentityData>
    implements $IdentityDataCopyWith<$Res> {
  _$IdentityDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? emailVerified = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? partyId = freezed,
    Object? partyTypeKey = freezed,
    Object? phone = freezed,
    Object? phoneVerified = freezed,
    Object? sub = freezed,
    Object? tenantCode = freezed,
  }) {
    return _then(
      _value.copyWith(
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            avatarUrl: freezed == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            emailVerified: freezed == emailVerified
                ? _value.emailVerified
                : emailVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            firstName: freezed == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastName: freezed == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String?,
            partyId: freezed == partyId
                ? _value.partyId
                : partyId // ignore: cast_nullable_to_non_nullable
                      as String?,
            partyTypeKey: freezed == partyTypeKey
                ? _value.partyTypeKey
                : partyTypeKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            phone: freezed == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String?,
            phoneVerified: freezed == phoneVerified
                ? _value.phoneVerified
                : phoneVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            sub: freezed == sub
                ? _value.sub
                : sub // ignore: cast_nullable_to_non_nullable
                      as String?,
            tenantCode: freezed == tenantCode
                ? _value.tenantCode
                : tenantCode // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$IdentityDataImplCopyWith<$Res>
    implements $IdentityDataCopyWith<$Res> {
  factory _$$IdentityDataImplCopyWith(
    _$IdentityDataImpl value,
    $Res Function(_$IdentityDataImpl) then,
  ) = __$$IdentityDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
  });
}

/// @nodoc
class __$$IdentityDataImplCopyWithImpl<$Res>
    extends _$IdentityDataCopyWithImpl<$Res, _$IdentityDataImpl>
    implements _$$IdentityDataImplCopyWith<$Res> {
  __$$IdentityDataImplCopyWithImpl(
    _$IdentityDataImpl _value,
    $Res Function(_$IdentityDataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of IdentityData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? emailVerified = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? partyId = freezed,
    Object? partyTypeKey = freezed,
    Object? phone = freezed,
    Object? phoneVerified = freezed,
    Object? sub = freezed,
    Object? tenantCode = freezed,
  }) {
    return _then(
      _$IdentityDataImpl(
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        avatarUrl: freezed == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        emailVerified: freezed == emailVerified
            ? _value.emailVerified
            : emailVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        firstName: freezed == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastName: freezed == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String?,
        partyId: freezed == partyId
            ? _value.partyId
            : partyId // ignore: cast_nullable_to_non_nullable
                  as String?,
        partyTypeKey: freezed == partyTypeKey
            ? _value.partyTypeKey
            : partyTypeKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        phone: freezed == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String?,
        phoneVerified: freezed == phoneVerified
            ? _value.phoneVerified
            : phoneVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        sub: freezed == sub
            ? _value.sub
            : sub // ignore: cast_nullable_to_non_nullable
                  as String?,
        tenantCode: freezed == tenantCode
            ? _value.tenantCode
            : tenantCode // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentityDataImpl implements _IdentityData {
  const _$IdentityDataImpl({
    @JsonKey(name: "address") this.address,
    @JsonKey(name: "avatar_url") this.avatarUrl,
    @JsonKey(name: "email") this.email,
    @JsonKey(name: "email_verified") this.emailVerified,
    @JsonKey(name: "first_name") this.firstName,
    @JsonKey(name: "last_name") this.lastName,
    @JsonKey(name: "party_id") this.partyId,
    @JsonKey(name: "party_type_key") this.partyTypeKey,
    @JsonKey(name: "phone") this.phone,
    @JsonKey(name: "phone_verified") this.phoneVerified,
    @JsonKey(name: "sub") this.sub,
    @JsonKey(name: "tenant_code") this.tenantCode,
  });

  factory _$IdentityDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentityDataImplFromJson(json);

  @override
  @JsonKey(name: "address")
  final String? address;
  @override
  @JsonKey(name: "avatar_url")
  final String? avatarUrl;
  @override
  @JsonKey(name: "email")
  final String? email;
  @override
  @JsonKey(name: "email_verified")
  final bool? emailVerified;
  @override
  @JsonKey(name: "first_name")
  final String? firstName;
  @override
  @JsonKey(name: "last_name")
  final String? lastName;
  @override
  @JsonKey(name: "party_id")
  final String? partyId;
  @override
  @JsonKey(name: "party_type_key")
  final String? partyTypeKey;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "phone_verified")
  final bool? phoneVerified;
  @override
  @JsonKey(name: "sub")
  final String? sub;
  @override
  @JsonKey(name: "tenant_code")
  final String? tenantCode;

  @override
  String toString() {
    return 'IdentityData(address: $address, avatarUrl: $avatarUrl, email: $email, emailVerified: $emailVerified, firstName: $firstName, lastName: $lastName, partyId: $partyId, partyTypeKey: $partyTypeKey, phone: $phone, phoneVerified: $phoneVerified, sub: $sub, tenantCode: $tenantCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityDataImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailVerified, emailVerified) ||
                other.emailVerified == emailVerified) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.partyId, partyId) || other.partyId == partyId) &&
            (identical(other.partyTypeKey, partyTypeKey) ||
                other.partyTypeKey == partyTypeKey) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phoneVerified, phoneVerified) ||
                other.phoneVerified == phoneVerified) &&
            (identical(other.sub, sub) || other.sub == sub) &&
            (identical(other.tenantCode, tenantCode) ||
                other.tenantCode == tenantCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    avatarUrl,
    email,
    emailVerified,
    firstName,
    lastName,
    partyId,
    partyTypeKey,
    phone,
    phoneVerified,
    sub,
    tenantCode,
  );

  /// Create a copy of IdentityData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityDataImplCopyWith<_$IdentityDataImpl> get copyWith =>
      __$$IdentityDataImplCopyWithImpl<_$IdentityDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentityDataImplToJson(this);
  }
}

abstract class _IdentityData implements IdentityData {
  const factory _IdentityData({
    @JsonKey(name: "address") final String? address,
    @JsonKey(name: "avatar_url") final String? avatarUrl,
    @JsonKey(name: "email") final String? email,
    @JsonKey(name: "email_verified") final bool? emailVerified,
    @JsonKey(name: "first_name") final String? firstName,
    @JsonKey(name: "last_name") final String? lastName,
    @JsonKey(name: "party_id") final String? partyId,
    @JsonKey(name: "party_type_key") final String? partyTypeKey,
    @JsonKey(name: "phone") final String? phone,
    @JsonKey(name: "phone_verified") final bool? phoneVerified,
    @JsonKey(name: "sub") final String? sub,
    @JsonKey(name: "tenant_code") final String? tenantCode,
  }) = _$IdentityDataImpl;

  factory _IdentityData.fromJson(Map<String, dynamic> json) =
      _$IdentityDataImpl.fromJson;

  @override
  @JsonKey(name: "address")
  String? get address;
  @override
  @JsonKey(name: "avatar_url")
  String? get avatarUrl;
  @override
  @JsonKey(name: "email")
  String? get email;
  @override
  @JsonKey(name: "email_verified")
  bool? get emailVerified;
  @override
  @JsonKey(name: "first_name")
  String? get firstName;
  @override
  @JsonKey(name: "last_name")
  String? get lastName;
  @override
  @JsonKey(name: "party_id")
  String? get partyId;
  @override
  @JsonKey(name: "party_type_key")
  String? get partyTypeKey;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "phone_verified")
  bool? get phoneVerified;
  @override
  @JsonKey(name: "sub")
  String? get sub;
  @override
  @JsonKey(name: "tenant_code")
  String? get tenantCode;

  /// Create a copy of IdentityData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityDataImplCopyWith<_$IdentityDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserMetadata _$UserMetadataFromJson(Map<String, dynamic> json) {
  return _UserMetadata.fromJson(json);
}

/// @nodoc
mixin _$UserMetadata {
  @JsonKey(name: "address")
  String? get address => throw _privateConstructorUsedError;
  @JsonKey(name: "avatar_url")
  String? get avatarUrl => throw _privateConstructorUsedError;
  @JsonKey(name: "email")
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: "email_verified")
  bool? get emailVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "first_name")
  String? get firstName => throw _privateConstructorUsedError;
  @JsonKey(name: "last_name")
  String? get lastName => throw _privateConstructorUsedError;
  @JsonKey(name: "party_id")
  String? get partyId => throw _privateConstructorUsedError;
  @JsonKey(name: "party_type_key")
  String? get partyTypeKey => throw _privateConstructorUsedError;
  @JsonKey(name: "phone")
  String? get phone => throw _privateConstructorUsedError;
  @JsonKey(name: "phone_verified")
  bool? get phoneVerified => throw _privateConstructorUsedError;
  @JsonKey(name: "sub")
  String? get sub => throw _privateConstructorUsedError;
  @JsonKey(name: "tenant_code")
  String? get tenantCode => throw _privateConstructorUsedError;
  @JsonKey(name: "tenant_id")
  String? get tenantId => throw _privateConstructorUsedError;

  /// Serializes this UserMetadata to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserMetadataCopyWith<UserMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserMetadataCopyWith<$Res> {
  factory $UserMetadataCopyWith(
    UserMetadata value,
    $Res Function(UserMetadata) then,
  ) = _$UserMetadataCopyWithImpl<$Res, UserMetadata>;
  @useResult
  $Res call({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
    @JsonKey(name: "tenant_id") String? tenantId,
  });
}

/// @nodoc
class _$UserMetadataCopyWithImpl<$Res, $Val extends UserMetadata>
    implements $UserMetadataCopyWith<$Res> {
  _$UserMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? emailVerified = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? partyId = freezed,
    Object? partyTypeKey = freezed,
    Object? phone = freezed,
    Object? phoneVerified = freezed,
    Object? sub = freezed,
    Object? tenantCode = freezed,
    Object? tenantId = freezed,
  }) {
    return _then(
      _value.copyWith(
            address: freezed == address
                ? _value.address
                : address // ignore: cast_nullable_to_non_nullable
                      as String?,
            avatarUrl: freezed == avatarUrl
                ? _value.avatarUrl
                : avatarUrl // ignore: cast_nullable_to_non_nullable
                      as String?,
            email: freezed == email
                ? _value.email
                : email // ignore: cast_nullable_to_non_nullable
                      as String?,
            emailVerified: freezed == emailVerified
                ? _value.emailVerified
                : emailVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            firstName: freezed == firstName
                ? _value.firstName
                : firstName // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastName: freezed == lastName
                ? _value.lastName
                : lastName // ignore: cast_nullable_to_non_nullable
                      as String?,
            partyId: freezed == partyId
                ? _value.partyId
                : partyId // ignore: cast_nullable_to_non_nullable
                      as String?,
            partyTypeKey: freezed == partyTypeKey
                ? _value.partyTypeKey
                : partyTypeKey // ignore: cast_nullable_to_non_nullable
                      as String?,
            phone: freezed == phone
                ? _value.phone
                : phone // ignore: cast_nullable_to_non_nullable
                      as String?,
            phoneVerified: freezed == phoneVerified
                ? _value.phoneVerified
                : phoneVerified // ignore: cast_nullable_to_non_nullable
                      as bool?,
            sub: freezed == sub
                ? _value.sub
                : sub // ignore: cast_nullable_to_non_nullable
                      as String?,
            tenantCode: freezed == tenantCode
                ? _value.tenantCode
                : tenantCode // ignore: cast_nullable_to_non_nullable
                      as String?,
            tenantId: freezed == tenantId
                ? _value.tenantId
                : tenantId // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$UserMetadataImplCopyWith<$Res>
    implements $UserMetadataCopyWith<$Res> {
  factory _$$UserMetadataImplCopyWith(
    _$UserMetadataImpl value,
    $Res Function(_$UserMetadataImpl) then,
  ) = __$$UserMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "avatar_url") String? avatarUrl,
    @JsonKey(name: "email") String? email,
    @JsonKey(name: "email_verified") bool? emailVerified,
    @JsonKey(name: "first_name") String? firstName,
    @JsonKey(name: "last_name") String? lastName,
    @JsonKey(name: "party_id") String? partyId,
    @JsonKey(name: "party_type_key") String? partyTypeKey,
    @JsonKey(name: "phone") String? phone,
    @JsonKey(name: "phone_verified") bool? phoneVerified,
    @JsonKey(name: "sub") String? sub,
    @JsonKey(name: "tenant_code") String? tenantCode,
    @JsonKey(name: "tenant_id") String? tenantId,
  });
}

/// @nodoc
class __$$UserMetadataImplCopyWithImpl<$Res>
    extends _$UserMetadataCopyWithImpl<$Res, _$UserMetadataImpl>
    implements _$$UserMetadataImplCopyWith<$Res> {
  __$$UserMetadataImplCopyWithImpl(
    _$UserMetadataImpl _value,
    $Res Function(_$UserMetadataImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of UserMetadata
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = freezed,
    Object? avatarUrl = freezed,
    Object? email = freezed,
    Object? emailVerified = freezed,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? partyId = freezed,
    Object? partyTypeKey = freezed,
    Object? phone = freezed,
    Object? phoneVerified = freezed,
    Object? sub = freezed,
    Object? tenantCode = freezed,
    Object? tenantId = freezed,
  }) {
    return _then(
      _$UserMetadataImpl(
        address: freezed == address
            ? _value.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String?,
        avatarUrl: freezed == avatarUrl
            ? _value.avatarUrl
            : avatarUrl // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _value.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        emailVerified: freezed == emailVerified
            ? _value.emailVerified
            : emailVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        firstName: freezed == firstName
            ? _value.firstName
            : firstName // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastName: freezed == lastName
            ? _value.lastName
            : lastName // ignore: cast_nullable_to_non_nullable
                  as String?,
        partyId: freezed == partyId
            ? _value.partyId
            : partyId // ignore: cast_nullable_to_non_nullable
                  as String?,
        partyTypeKey: freezed == partyTypeKey
            ? _value.partyTypeKey
            : partyTypeKey // ignore: cast_nullable_to_non_nullable
                  as String?,
        phone: freezed == phone
            ? _value.phone
            : phone // ignore: cast_nullable_to_non_nullable
                  as String?,
        phoneVerified: freezed == phoneVerified
            ? _value.phoneVerified
            : phoneVerified // ignore: cast_nullable_to_non_nullable
                  as bool?,
        sub: freezed == sub
            ? _value.sub
            : sub // ignore: cast_nullable_to_non_nullable
                  as String?,
        tenantCode: freezed == tenantCode
            ? _value.tenantCode
            : tenantCode // ignore: cast_nullable_to_non_nullable
                  as String?,
        tenantId: freezed == tenantId
            ? _value.tenantId
            : tenantId // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$UserMetadataImpl implements _UserMetadata {
  const _$UserMetadataImpl({
    @JsonKey(name: "address") this.address,
    @JsonKey(name: "avatar_url") this.avatarUrl,
    @JsonKey(name: "email") this.email,
    @JsonKey(name: "email_verified") this.emailVerified,
    @JsonKey(name: "first_name") this.firstName,
    @JsonKey(name: "last_name") this.lastName,
    @JsonKey(name: "party_id") this.partyId,
    @JsonKey(name: "party_type_key") this.partyTypeKey,
    @JsonKey(name: "phone") this.phone,
    @JsonKey(name: "phone_verified") this.phoneVerified,
    @JsonKey(name: "sub") this.sub,
    @JsonKey(name: "tenant_code") this.tenantCode,
    @JsonKey(name: "tenant_id") this.tenantId,
  });

  factory _$UserMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserMetadataImplFromJson(json);

  @override
  @JsonKey(name: "address")
  final String? address;
  @override
  @JsonKey(name: "avatar_url")
  final String? avatarUrl;
  @override
  @JsonKey(name: "email")
  final String? email;
  @override
  @JsonKey(name: "email_verified")
  final bool? emailVerified;
  @override
  @JsonKey(name: "first_name")
  final String? firstName;
  @override
  @JsonKey(name: "last_name")
  final String? lastName;
  @override
  @JsonKey(name: "party_id")
  final String? partyId;
  @override
  @JsonKey(name: "party_type_key")
  final String? partyTypeKey;
  @override
  @JsonKey(name: "phone")
  final String? phone;
  @override
  @JsonKey(name: "phone_verified")
  final bool? phoneVerified;
  @override
  @JsonKey(name: "sub")
  final String? sub;
  @override
  @JsonKey(name: "tenant_code")
  final String? tenantCode;
  @override
  @JsonKey(name: "tenant_id")
  final String? tenantId;

  @override
  String toString() {
    return 'UserMetadata(address: $address, avatarUrl: $avatarUrl, email: $email, emailVerified: $emailVerified, firstName: $firstName, lastName: $lastName, partyId: $partyId, partyTypeKey: $partyTypeKey, phone: $phone, phoneVerified: $phoneVerified, sub: $sub, tenantCode: $tenantCode, tenantId: $tenantId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserMetadataImpl &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.emailVerified, emailVerified) ||
                other.emailVerified == emailVerified) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.partyId, partyId) || other.partyId == partyId) &&
            (identical(other.partyTypeKey, partyTypeKey) ||
                other.partyTypeKey == partyTypeKey) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.phoneVerified, phoneVerified) ||
                other.phoneVerified == phoneVerified) &&
            (identical(other.sub, sub) || other.sub == sub) &&
            (identical(other.tenantCode, tenantCode) ||
                other.tenantCode == tenantCode) &&
            (identical(other.tenantId, tenantId) ||
                other.tenantId == tenantId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    avatarUrl,
    email,
    emailVerified,
    firstName,
    lastName,
    partyId,
    partyTypeKey,
    phone,
    phoneVerified,
    sub,
    tenantCode,
    tenantId,
  );

  /// Create a copy of UserMetadata
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserMetadataImplCopyWith<_$UserMetadataImpl> get copyWith =>
      __$$UserMetadataImplCopyWithImpl<_$UserMetadataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserMetadataImplToJson(this);
  }
}

abstract class _UserMetadata implements UserMetadata {
  const factory _UserMetadata({
    @JsonKey(name: "address") final String? address,
    @JsonKey(name: "avatar_url") final String? avatarUrl,
    @JsonKey(name: "email") final String? email,
    @JsonKey(name: "email_verified") final bool? emailVerified,
    @JsonKey(name: "first_name") final String? firstName,
    @JsonKey(name: "last_name") final String? lastName,
    @JsonKey(name: "party_id") final String? partyId,
    @JsonKey(name: "party_type_key") final String? partyTypeKey,
    @JsonKey(name: "phone") final String? phone,
    @JsonKey(name: "phone_verified") final bool? phoneVerified,
    @JsonKey(name: "sub") final String? sub,
    @JsonKey(name: "tenant_code") final String? tenantCode,
    @JsonKey(name: "tenant_id") final String? tenantId,
  }) = _$UserMetadataImpl;

  factory _UserMetadata.fromJson(Map<String, dynamic> json) =
      _$UserMetadataImpl.fromJson;

  @override
  @JsonKey(name: "address")
  String? get address;
  @override
  @JsonKey(name: "avatar_url")
  String? get avatarUrl;
  @override
  @JsonKey(name: "email")
  String? get email;
  @override
  @JsonKey(name: "email_verified")
  bool? get emailVerified;
  @override
  @JsonKey(name: "first_name")
  String? get firstName;
  @override
  @JsonKey(name: "last_name")
  String? get lastName;
  @override
  @JsonKey(name: "party_id")
  String? get partyId;
  @override
  @JsonKey(name: "party_type_key")
  String? get partyTypeKey;
  @override
  @JsonKey(name: "phone")
  String? get phone;
  @override
  @JsonKey(name: "phone_verified")
  bool? get phoneVerified;
  @override
  @JsonKey(name: "sub")
  String? get sub;
  @override
  @JsonKey(name: "tenant_code")
  String? get tenantCode;
  @override
  @JsonKey(name: "tenant_id")
  String? get tenantId;

  /// Create a copy of UserMetadata
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserMetadataImplCopyWith<_$UserMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
