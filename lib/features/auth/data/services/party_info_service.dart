import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';

class PartyInfoService {
  /// Updates party information in Supabase
  /// Returns true if successful, false otherwise
  static Future<bool> updatePartyInfo({
    required String firstName,
    required String lastName,
    required String email,
    required String address,
    required String phone,
    required String username,
    required String tenantCode,
    required String rolename,
    required String avatarUrl,
    required String partyTypeKey,
    required String partyId,
    required String userId,
    required String accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}${ApiConfig.updatePartyInfoEndpoint}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        'Authorization': 'Bearer $accessToken',
      };

      final body = {
        'p_first_name': firstName,
        'p_last_name': lastName,
        'p_email': email,
        'p_address': address,
        'p_phone': phone,
        'p_username': username,
        'p_tenant_code': tenantCode,
        'p_rolename': rolename,
        'p_avatar_url': avatarUrl,
        'p_party_type_key': partyTypeKey,
        'p_party_id': null,
        'p_user_id': userId,
      };

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('Party info updated successfully');
        return true;
      } else {
        print(
          'Failed to update party info. Status: ${response.statusCode}, Response: ${response.body}',
        );
        return false;
      }
    } catch (e) {
      print('Error updating party info: $e');
      return false;
    }
  }
}
