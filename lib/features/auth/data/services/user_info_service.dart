import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';

class UserInfoService {
  static Future<Map<String, dynamic>?> getUserInfo({
    required String userId,
    required String accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.getUserInfo}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        'Authorization': 'Bearer $accessToken',
      };

      final body = {"user_id": userId};

      print('🔄 Getting user info for: $userId');

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

             if (response.statusCode == 200) {
         final responseData = jsonDecode(response.body);
         print('✅ User info response: $responseData');

         // Extract data from the response structure
         if (responseData is Map && responseData['success'] == true && responseData['data'] != null) {
           final userData = Map<String, dynamic>.from(responseData['data']);
           print('✅ Extracted user data: $userData');
           return userData;
         }
         return null;
       } else {
        print(
          '❌ Failed to load user info. Status: ${response.statusCode}, Response: ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('💥 Error loading user info: $e');
      return null;
    }
  }
}
