import 'package:google_sign_in/google_sign_in.dart';
import 'dart:async';

/// Result of a Google sign-in attempt
class GoogleSignInResult {
  final String? idToken;
  final String? accessToken;
  final String? displayName;
  final String? email;
  final String? photoUrl;
  final GoogleSignInAccount? account;
  final String? error;

  GoogleSignInResult({
    this.idToken,
    this.accessToken,
    this.displayName,
    this.email,
    this.photoUrl,
    this.account,
    this.error,
  });

  bool get isSuccess => (idToken?.isNotEmpty ?? false) && error == null;
}

class GoogleSignInService {
  final String? clientId;
  final String? serverClientId;
  final List<String> scopes;

  GoogleSignInService({
    this.clientId,
    this.serverClientId,
    this.scopes = const ['email', 'profile'],
  });

  Future<GoogleSignInResult> signInWithGoogle() async {
    try {
      final GoogleSignIn signIn = GoogleSignIn.instance;
      await signIn.initialize(
        clientId: clientId,
        serverClientId: serverClientId,
      );
      final completer = Completer<GoogleSignInAccount?>();
      late final StreamSubscription sub;
      sub = signIn.authenticationEvents.listen((event) async {
        if (event is GoogleSignInAuthenticationEventSignIn) {
          completer.complete(event.user);
          await sub.cancel();
        } else if (event is GoogleSignInAuthenticationEventSignOut) {
          completer.complete(null);
          await sub.cancel();
        }
      });
      await signIn.authenticate();
      final GoogleSignInAccount? googleUser = await completer.future;
      if (googleUser == null) {
        return GoogleSignInResult(error: 'Google sign-in was canceled');
      }
      final authClient = googleUser.authorizationClient;
      final auth = await authClient.authorizationForScopes(scopes);
      final accessToken = auth?.accessToken;
      final idToken = await googleUser.authentication.idToken;
      final displayName = googleUser.displayName;
      final email = googleUser.email;
      final photoUrl = googleUser.photoUrl;
      print('googleUser: $googleUser');
      print('Signed in: $displayName');
      print('Email: $email');
      print('Photo URL: $photoUrl');
      return GoogleSignInResult(
        idToken: idToken,
        accessToken: accessToken,
        displayName: displayName,
        email: email,
        photoUrl: photoUrl,
        account: googleUser,
      );
    } catch (error) {
      print('Google sign-in error: $error');
      return GoogleSignInResult(error: error.toString());
    }
  }
} 