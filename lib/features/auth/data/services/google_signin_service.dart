import 'package:google_sign_in/google_sign_in.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// Result of a Google sign-in attempt
class GoogleSignInResult {
  final String? idToken;
  final String? accessToken;
  final String? displayName;
  final String? email;
  final String? photoUrl;
  final GoogleSignInAccount? account;
  final String? error;

  GoogleSignInResult({
    this.idToken,
    this.accessToken,
    this.displayName,
    this.email,
    this.photoUrl,
    this.account,
    this.error,
  });

  bool get isSuccess => (idToken?.isNotEmpty ?? false) && error == null;
}

class GoogleSignInService {
  final String? clientId;
  final String? serverClientId;
  final List<String> scopes;

  GoogleSignInService({
    this.clientId,
    this.serverClientId,
    this.scopes = const ['email', 'profile'],
  });

  Future<GoogleSignInResult> signInWithGoogle() async {
    print('🔵 Starting Google Sign-In process...');
    print('🔵 Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
    print('🔵 Client ID: $clientId');
    print('🔵 Server Client ID: $serverClientId');
    print('🔵 Scopes: $scopes');

    try {
      final GoogleSignIn signIn = GoogleSignIn.instance;
      print('🔵 GoogleSignIn instance created');

      // Initialize GoogleSignIn with platform-specific configuration
      print('🔵 Initializing GoogleSignIn...');
      if (kIsWeb) {
        print('🔵 Web initialization with clientId: $clientId');
        await signIn.initialize(
          clientId: clientId,
        );
      } else {
        print('🔵 Mobile initialization with clientId: $clientId, serverClientId: $serverClientId');
        await signIn.initialize(
          clientId: clientId,
          serverClientId: serverClientId,
        );
      }
      print('✅ GoogleSignIn initialized successfully');

      // Check if platform supports authenticate method
      final supportsAuth = signIn.supportsAuthenticate();
      print('🔵 Platform supports authenticate: $supportsAuth');

      GoogleSignInAccount? googleUser;

      if (supportsAuth) {
        // Use authenticate method for platforms that support it
        print('🔵 Starting authentication with authenticate()...');
        googleUser = await signIn.authenticate(
          scopeHint: scopes,
        );
        print('✅ Authentication with authenticate() successful');
      } else if (kIsWeb) {
        // For web, we need to use a different approach
        print('🔵 Web platform detected, using web-specific sign-in flow...');

        // First try lightweight authentication
        print('🔵 Attempting lightweight authentication...');
        final lightweightResult = signIn.attemptLightweightAuthentication();

        GoogleSignInAccount? lightweightUser;
        if (lightweightResult is Future<GoogleSignInAccount?>) {
          lightweightUser = await lightweightResult;
        } else {
          lightweightUser = lightweightResult as GoogleSignInAccount?;
        }

        if (lightweightUser != null) {
          print('✅ Lightweight authentication successful');
          googleUser = lightweightUser;
        } else {
          // If lightweight auth fails on web, we need to show an error
          // because web requires explicit user interaction through Google's UI
          print('❌ Web platform requires user interaction through Google Sign-In button');
          print('❌ This error typically occurs when:');
          print('   1. The domain is not authorized in Google Cloud Console');
          print('   2. The user needs to explicitly click a Google Sign-In button');
          print('   3. Third-party cookies are blocked');

          return GoogleSignInResult(
            error: 'Web authentication requires user interaction. Please ensure:\n'
                   '1. Your domain is authorized in Google Cloud Console\n'
                   '2. Third-party cookies are enabled\n'
                   '3. Try refreshing the page and clicking the button again'
          );
        }
      } else {
        print('❌ Platform not supported');
        return GoogleSignInResult(error: 'Platform not supported');
      }

      if (googleUser == null) {
        print('❌ Authentication failed - user is null');
        return GoogleSignInResult(error: 'Authentication failed');
      }

      print('✅ Authentication successful');
      print('🔵 User: ${googleUser.displayName} (${googleUser.email})');

      // Get authentication details (now synchronous in v7)
      print('🔵 Getting authentication details...');
      final GoogleSignInAuthentication googleAuth = googleUser.authentication;
      print('✅ Authentication details retrieved');

      // Get access token for the specified scopes
      print('🔵 Getting authorization client...');
      final authClient = signIn.authorizationClient;
      print('🔵 Requesting authorization for scopes: $scopes');
      final authorization = await authClient.authorizationForScopes(scopes);
      print('🔵 Authorization result: ${authorization != null ? 'Success' : 'Failed'}');

      final accessToken = authorization?.accessToken;
      final idToken = googleAuth.idToken;
      final displayName = googleUser.displayName;
      final email = googleUser.email;
      final photoUrl = googleUser.photoUrl;

      print('🔵 Final results:');
      print('  - Display Name: $displayName');
      print('  - Email: $email');
      print('  - Photo URL: $photoUrl');
      print('  - ID Token: ${idToken != null ? 'Present (${idToken?.substring(0, 20)}...)' : 'Missing'}');
      print('  - Access Token: ${accessToken != null ? 'Present (${accessToken?.substring(0, 20)}...)' : 'Missing'}');

      if (idToken == null) {
        print('❌ ID Token is null - this will cause authentication to fail');
        return GoogleSignInResult(error: 'ID Token is null');
      }

      print('✅ Google Sign-In completed successfully');
      return GoogleSignInResult(
        idToken: idToken,
        accessToken: accessToken,
        displayName: displayName,
        email: email,
        photoUrl: photoUrl,
        account: googleUser,
      );
    } catch (error, stackTrace) {
      print('❌ Google sign-in error: $error');
      print('❌ Stack trace: $stackTrace');
      return GoogleSignInResult(error: error.toString());
    }
  }
} 