import 'package:google_sign_in/google_sign_in.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

/// Result of a Google sign-in attempt
class GoogleSignInResult {
  final String? idToken;
  final String? accessToken;
  final String? displayName;
  final String? email;
  final String? photoUrl;
  final GoogleSignInAccount? account;
  final String? error;

  GoogleSignInResult({
    this.idToken,
    this.accessToken,
    this.displayName,
    this.email,
    this.photoUrl,
    this.account,
    this.error,
  });

  bool get isSuccess => (idToken?.isNotEmpty ?? false) && error == null;
}

class GoogleSignInService {
  final String? clientId;
  final String? serverClientId;
  final List<String> scopes;

  GoogleSignInService({
    this.clientId,
    this.serverClientId,
    this.scopes = const ['email', 'profile'],
  });

  Future<GoogleSignInResult> signInWithGoogle() async {
    try {
      final GoogleSignIn signIn = GoogleSignIn.instance;

      // Initialize GoogleSignIn with platform-specific configuration
      if (kIsWeb) {
        await signIn.initialize(
          clientId: clientId,
        );
      } else {
        await signIn.initialize(
          clientId: clientId,
          serverClientId: serverClientId,
        );
      }

      // Check if platform supports authenticate method
      if (!signIn.supportsAuthenticate()) {
        return GoogleSignInResult(error: 'Platform does not support authenticate method');
      }

      // Authenticate the user
      final GoogleSignInAccount googleUser = await signIn.authenticate(
        scopeHint: scopes,
      );

      // Get authentication details (now synchronous in v7)
      final GoogleSignInAuthentication googleAuth = googleUser.authentication;

      // Get access token for the specified scopes
      final authClient = signIn.authorizationClient;
      final authorization = await authClient.authorizationForScopes(scopes);

      final accessToken = authorization?.accessToken;
      final idToken = googleAuth.idToken;
      final displayName = googleUser.displayName;
      final email = googleUser.email;
      final photoUrl = googleUser.photoUrl;

      print('googleUser: $googleUser');
      print('Signed in: $displayName');
      print('Email: $email');
      print('Photo URL: $photoUrl');
      print('ID Token: ${idToken != null ? 'Present' : 'Missing'}');
      print('Access Token: ${accessToken != null ? 'Present' : 'Missing'}');

      return GoogleSignInResult(
        idToken: idToken,
        accessToken: accessToken,
        displayName: displayName,
        email: email,
        photoUrl: photoUrl,
        account: googleUser,
      );
    } catch (error) {
      print('Google sign-in error: $error');
      return GoogleSignInResult(error: error.toString());
    }
  }
} 