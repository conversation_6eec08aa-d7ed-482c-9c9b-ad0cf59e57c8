import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/auth/data/model/login/login_model.dart';
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart'
    as signup;
import 'package:recallloop/features/auth/data/model/signup/signup_model.dart'
    hide User;
import 'package:recallloop/features/auth/data/services/google_signin_service.dart';
import 'package:recallloop/features/auth/data/services/login_service.dart';
import 'package:recallloop/features/auth/data/services/party_info_service.dart';
import 'package:recallloop/features/auth/data/services/user_info_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;

part 'auth_bloc.freezed.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  // final LocalUserService _hiveUserService = LocalUserService();
  final HiveUserService _hiveUserService = HiveUserService();

  AuthBloc() : super(const _Initial()) {
    on<AuthEvent>((event, emit) async {
      try {
        if (event is _LoginRequested) {
          emit(const _Loading());

          final loginService = LoginService();
          final loginModel = await loginService.login(
            event.email,
            event.password,
          );

          if (loginModel != null) {
            // Get additional user info after successful login
            final userInfo = await UserInfoService.getUserInfo(
              userId: loginModel.user?.id ?? '',
              accessToken: loginModel.accessToken ?? '',
            );

            if (userInfo != null) {
              // Save user info to local storage
              // await _hiveUserService.saveUserInfo(userInfo);

              // Update the login model with additional user info from fn_get_user_info
              final updatedLoginModel = loginModel.copyWith(
                user: loginModel.user?.copyWith(
                  userMetadata: loginModel.user?.userMetadata?.copyWith(
                    tenantCode: userInfo['tenant_code'],
                    tenantId:
                        userInfo['tenant_id'], // Use parent_party_id as tenantId
                    partyTypeKey: userInfo['party_type_key'],
                    partyId: userInfo['id'], // Use id as partyId
                  ),
                  updatedAt: DateTime.now().toIso8601String(),
                  isAnonymous: loginModel.user?.isAnonymous ?? false,
                ),
              );

              // Save updated user data to local database
              await _hiveUserService.saveUserAfterLogin(updatedLoginModel);
              emit(_Authenticated(loginUser: updatedLoginModel));
            } else {
              // Save original user data if user info call fails
              await _hiveUserService.saveUserAfterLogin(loginModel);
              emit(_Authenticated(loginUser: loginModel));
            }
          } else {
            emit(_Error('Invalid email or password'));
          }
        }

        if (event is _SignUpRequested) {
          emit(const _Loading());

          final email = event.email;
          final password = event.password;
          final firstName = event.firstName;
          final lastName = event.lastName;
          final phone = event.phone;
          final address = event.address;

          final signUpResponse = await Supabase.instance.client.auth.signUp(
            email: email,
            password: password,
          );

          if (signUpResponse.user == null) {
            emit(_SignUpError('Sign up failed. Please try again.'));
            return;
          }

          // Get the unique user ID from the response
          final userId = signUpResponse.user!.id;

          // Generate username from first and last name
          final username = '${firstName.toLowerCase()}${lastName.toLowerCase()}'
              .replaceAll(' ', '');

          // Update party info in Supabase
          final partyInfoUpdateSuccess = await PartyInfoService.updatePartyInfo(
            firstName: firstName,
            lastName: lastName,
            email: email,
            address: address,
            phone: phone,
            username: username,
            tenantCode: 'TN-0001',
            rolename: 'PLAYER',
            avatarUrl: '',
            partyTypeKey: 'PLAYER',
            partyId: "",
            userId: userId,
            accessToken: signUpResponse.session?.accessToken ?? '',
          );

          if (partyInfoUpdateSuccess) {
            emit(
              _SignUpSuccess(
                signupUser: signup.SignupModel(
                  accessToken: signUpResponse.session?.accessToken ?? '',
                  user: signup.User(
                    id: userId,
                    email: email,
                    userMetadata: signup.Data(
                      firstName: firstName,
                      lastName: lastName,
                      phone: phone,
                      address: address,
                    ),
                  ),
                ),
              ),
            );
          } else {
            emit(
              _SignUpError(
                'Sign up completed but failed to update party info. Please contact support.',
              ),
            );
          }
        }

        if (event is _LogoutRequested) {
          emit(const _Loading());

          // Clear user data from local database (this also clears user info)
          await _hiveUserService.logout();

          // Clear tenant_code from SharedPreferences
          try {
            final prefs = await SharedPreferences.getInstance();
            await prefs.remove('tenant_code');
          } catch (e) {
            print('Error clearing tenant code during logout: $e');
          }

          emit(const _Unauthenticated());
        }

        if (event is _CheckAuthStatus) {
          emit(const _Loading());

          // Check if user is logged in with valid token
          final isLoggedIn = await _hiveUserService.isLoggedIn();
          if (isLoggedIn) {
            final currentUser = await _hiveUserService.getCurrentUser();
            if (currentUser != null) {
              // User is authenticated with valid token
              // For now, emit unauthenticated since we don't have a proper conversion
              // In a real implementation, you'd convert LocalUserEntity to LoginModel
              emit(const _Unauthenticated());
              // TODO: Create proper authenticated state from local user data
            } else {
              emit(const _Unauthenticated());
            }
          } else {
            emit(const _Unauthenticated());
          }
        }

        if (event is _GoogleSignInRequested) {
          print('🟡 AuthBloc: Google Sign-In requested');
          emit(const _Loading());

          // Use different client IDs for web and mobile
          print('🟡 AuthBloc: Creating GoogleSignInService for ${kIsWeb ? 'Web' : 'Mobile'}');
          final googleSignInService = kIsWeb
            ? GoogleSignInService(
                clientId: '401052732324-hl43icerb2oj6bugd52rgf5n4lgbv4oq.apps.googleusercontent.com',
              )
            : GoogleSignInService(
                serverClientId: '401052732324-hl43icerb2oj6bugd52rgf5n4lgbv4oq.apps.googleusercontent.com',
              );

          print('🟡 AuthBloc: Calling signInWithGoogle...');
          final googleResponse = await googleSignInService.signInWithGoogle();

          print('🟡 AuthBloc: Google response received');
          print('🟡 AuthBloc: Success: ${googleResponse.isSuccess}');
          print('🟡 AuthBloc: Error: ${googleResponse.error}');
          print('🟡 AuthBloc: ID Token present: ${googleResponse.idToken != null}');

          if (!googleResponse.isSuccess) {
            print('❌ AuthBloc: Google sign-in failed - ${googleResponse.error}');
            emit(_Error('Google sign-in failed: ${googleResponse.error}'));
            return;
          }

          // Authenticate with Supabase
          final supabaseResponse = await Supabase.instance.client.auth
              .signInWithIdToken(
                provider: OAuthProvider.google,
                idToken: googleResponse.idToken!,
              );

          if (supabaseResponse.user != null) {
            final displayName = googleResponse.displayName ?? '';
            final nameParts = displayName.split(' ');
            final firstName = nameParts.isNotEmpty ? nameParts.first : '';
            final partyid = supabaseResponse.user!.id;
            final lastName = nameParts.length > 1
                ? nameParts.sublist(1).join(' ')
                : '';
            final phone = '';
            final address = '';
            final email = supabaseResponse.user!.email ?? '';
            final username =
                '${firstName.toLowerCase()}${lastName.toLowerCase()}'
                    .replaceAll(' ', '');

            // Update party info in Supabase using the same API
            final partyInfoUpdateSuccess =
                await PartyInfoService.updatePartyInfo(
                  firstName: firstName,
                  lastName: lastName,
                  email: email,
                  address: address,
                  phone: phone,
                  username: username,
                  tenantCode: 'TN-0001',
                  rolename: 'PLAYER',
                  avatarUrl: googleResponse.photoUrl ?? '',
                  partyTypeKey: 'PLAYER',
                  partyId: "",
                  userId: partyid,
                  accessToken: supabaseResponse.session?.accessToken ?? '',
                );

            if (partyInfoUpdateSuccess) {
              final loginModel = LoginModel(
                accessToken: supabaseResponse.session?.accessToken ?? '',
                tokenType: supabaseResponse.session?.tokenType,
                expiresIn: supabaseResponse.session?.expiresIn,
                expiresAt: supabaseResponse.session?.expiresAt,
                refreshToken: supabaseResponse.session?.refreshToken,
                user: User(
                  id: partyid,
                  userMetadata: UserMetadata(
                    firstName: firstName,
                    lastName: lastName,
                    email: supabaseResponse.user!.email ?? '',
                    phone: phone,
                    address: address,
                    tenantCode: 'TN-0001',
                    avatarUrl: googleResponse.photoUrl ?? '',
                    partyTypeKey: 'PLAYER',
                    partyId: partyid,
                  ),
                ),
              );

              // Get additional user info after successful Google sign-in
              final userInfo = await UserInfoService.getUserInfo(
                userId: loginModel.user?.id ?? '',
                accessToken: loginModel.accessToken ?? '',
              );

              if (userInfo != null) {
                // Save user info to local storage
                // await _hiveUserService.saveUserInfo(userInfo);

                // Update the login model with additional user info from fn_get_user_info
                final updatedLoginModel = loginModel.copyWith(
                  user: loginModel.user?.copyWith(
                    userMetadata: loginModel.user?.userMetadata?.copyWith(
                      tenantCode: userInfo['tenant_code'],
                      tenantId:
                          userInfo['tenant_id'], // Use parent_party_id as tenantId
                      partyTypeKey: userInfo['party_type_key'],
                      partyId: userInfo['id'], // Use id as partyId
                    ),
                    updatedAt: DateTime.now().toIso8601String(),
                    isAnonymous: loginModel.user?.isAnonymous ?? false,
                  ),
                );

                // Save updated user data to local DB
                await _hiveUserService.saveUserAfterLogin(updatedLoginModel);
                emit(_Authenticated(loginUser: updatedLoginModel));
              } else {
                // Save original user data if user info call fails
                await _hiveUserService.saveUserAfterLogin(loginModel);
                emit(_Authenticated(loginUser: loginModel));
              }
              return;
            } else {
              emit(
                _Error(
                  'Google sign-in completed but failed to update party info. Please contact support.',
                ),
              );
              return;
            }
          } else {
            emit(_Error('Supabase authentication failed'));
          }
        }
      } catch (e) {
        emit(_Error('An unexpected error occurred: ${e.toString()}'));
      }
    });
  }
}
