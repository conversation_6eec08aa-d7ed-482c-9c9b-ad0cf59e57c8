import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth/auth_bloc.dart';

class GoogleSignInButton extends StatelessWidget {
  const GoogleSignInButton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      icon: Image.asset(
        'assets/images/google.png',
        height: 24, // Adjust size as needed
        width: 24,
      ),
      label: const Text('Sign in with Google'),
      onPressed: () {
        context.read<AuthBloc>().add(const AuthEvent.googleSignInRequested());
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        minimumSize: const Size(double.infinity, 48),
        side: const BorderSide(color: Colors.grey),
        textStyle: const TextStyle(fontSize: 16),
      ),
    );
  }
} 