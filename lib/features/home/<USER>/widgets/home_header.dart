import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../../../profile/domain/entities/user_profile.dart';

class HomeHeader extends StatelessWidget {
  const HomeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return ResponsiveLayout(
          mobile: _buildMobileHeader(context, state),
          tablet: _buildTabletHeader(context, state),
          desktop: _buildDesktopHeader(context, state),
        );
      },
    );
  }

  Widget _buildMobileHeader(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    _getGreeting(),
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  ResponsiveText(
                    _getUserName(state),
                    style: AppTheme.headlineMedium.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
            ),
            _buildProfileAvatar(state),
          ],
        ),
        SizedBox(height: AppTheme.spacing16),
        _buildStreakInfo(state),
      ],
    );
  }

  Widget _buildTabletHeader(BuildContext context, ProfileState state) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                _getGreeting(),
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: 8.h),
              ResponsiveText(
                _getUserName(state),
                style: AppTheme.displaySmall.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing12),
              _buildStreakInfo(state),
            ],
          ),
        ),
        SizedBox(width: AppTheme.spacing24),
        _buildProfileAvatar(state, size: 64.w),
      ],
    );
  }

  Widget _buildDesktopHeader(BuildContext context, ProfileState state) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                _getGreeting(),
                style: AppTheme.bodyLarge.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: 8.h),
              ResponsiveText(
                _getUserName(state),
                style: AppTheme.displayMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              Row(
                children: [
                  _buildStreakInfo(state),
                  SizedBox(width: AppTheme.spacing32),
                  _buildMotivationalQuote(),
                ],
              ),
            ],
          ),
        ),
        SizedBox(width: AppTheme.spacing32),
        _buildProfileAvatar(state, size: 80.w),
      ],
    );
  }

  Widget _buildProfileAvatar(ProfileState state, {double? size}) {
    final avatarSize = size ?? 48.w;
    
    return Container(
      width: avatarSize,
      height: avatarSize,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryVariant,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(avatarSize / 2),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: state.maybeWhen(
        loaded: (profile) => profile.avatarUrl != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(avatarSize / 2),
                child: Image.network(
                  profile.avatarUrl!,
                  width: avatarSize,
                  height: avatarSize,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(avatarSize),
                ),
              )
            : _buildDefaultAvatar(avatarSize),
        orElse: () => _buildDefaultAvatar(avatarSize),
      ),
    );
  }

  Widget _buildDefaultAvatar(double size) {
    return Icon(
      Icons.person,
      color: AppTheme.textOnPrimary,
      size: size * 0.6,
    );
  }

  Widget _buildStreakInfo(ProfileState state) {
    int streak = state.maybeWhen(
      loaded: (profile) => profile.exerciseStats.streak,
      orElse: () => 0,
    );

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing12,
        vertical: AppTheme.spacing8,
      ),
      decoration: BoxDecoration(
        color: AppTheme.successColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
        border: Border.all(
          color: AppTheme.successColor.withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.local_fire_department,
            color: AppTheme.successColor,
            size: 16.sp,
          ),
          SizedBox(width: AppTheme.spacing4),
          ResponsiveText(
            '$streak day streak',
            style: AppTheme.labelSmall.copyWith(
              color: AppTheme.successColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotivationalQuote() {
    final quotes = [
      "Your brain is a muscle. Exercise it daily!",
      "Small steps lead to big improvements.",
      "Consistency is the key to cognitive growth.",
      "Challenge your mind, expand your potential.",
      "Every exercise makes you stronger mentally.",
    ];

    final quote = quotes[DateTime.now().day % quotes.length];

    return Expanded(
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing12),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.3),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.format_quote,
              color: AppTheme.accentColor,
              size: 16.sp,
            ),
            SizedBox(width: AppTheme.spacing8),
            Expanded(
              child: ResponsiveText(
                quote,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning';
    } else if (hour < 17) {
      return 'Good afternoon';
    } else {
      return 'Good evening';
    }
  }

  String _getUserName(ProfileState state) {
    return state.when(
      initial: () => 'Welcome back',
      loading: () => 'Loading...',
      loaded: (profile) => profile.name,
      error: (message) => 'Welcome back',
    );
  }
}
