import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../../../profile/domain/entities/user_profile.dart';

class RecentActivitiesCard extends StatelessWidget {
  const RecentActivitiesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileBloc, ProfileState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.history,
                      color: AppTheme.secondaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Recent Activities',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildEmptyState(),
                  loading: () => _buildLoadingState(),
                  loaded: (profile) => _buildActivitiesList(profile),
                  error: (message) => _buildEmptyState(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 150.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.secondaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: 150.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history_outlined,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'No recent activities',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivitiesList(UserProfile profile) {
    // Generate mock recent activities based on profile data
    final activities = _generateRecentActivities(profile);

    if (activities.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: activities.map((activity) {
        return Padding(
          padding: EdgeInsets.only(bottom: AppTheme.spacing12),
          child: _buildActivityItem(activity),
        );
      }).toList(),
    );
  }

  Widget _buildActivityItem(ActivityItem activity) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: activity.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: activity.color.withOpacity(0.2),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: activity.color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20.sp,
            ),
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  activity.title,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 2.h),
                ResponsiveText(
                  activity.description,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              ResponsiveText(
                activity.timeAgo,
                style: AppTheme.labelSmall.copyWith(
                  color: AppTheme.textTertiary,
                ),
              ),
              if (activity.score != null) ...[
                SizedBox(height: 4.h),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 6.w,
                    vertical: 2.h,
                  ),
                  decoration: BoxDecoration(
                    color: _getScoreColor(activity.score!).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  ),
                  child: ResponsiveText(
                    '${activity.score}%',
                    style: AppTheme.labelSmall.copyWith(
                      color: _getScoreColor(activity.score!),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  List<ActivityItem> _generateRecentActivities(UserProfile profile) {
    final now = DateTime.now();
    
    return [
      ActivityItem(
        icon: Icons.psychology,
        title: 'Memory Matching',
        description: 'Completed cognitive exercise',
        timeAgo: '2h ago',
        color: AppTheme.primaryColor,
        score: profile.exerciseStats.memoryMatchingScore,
      ),
      ActivityItem(
        icon: Icons.mood,
        title: 'Mood Check-in',
        description: 'Logged mood as "Good"',
        timeAgo: '4h ago',
        color: AppTheme.accentColor,
      ),
      ActivityItem(
        icon: Icons.record_voice_over,
        title: 'Word Recall',
        description: 'Completed verbal exercise',
        timeAgo: '1d ago',
        color: AppTheme.secondaryColor,
        score: profile.exerciseStats.wordRecallScore,
      ),
      ActivityItem(
        icon: Icons.analytics,
        title: 'Progress Review',
        description: 'Viewed weekly progress report',
        timeAgo: '2d ago',
        color: AppTheme.warningColor,
      ),
      ActivityItem(
        icon: Icons.star,
        title: 'Achievement Unlocked',
        description: '7-day streak milestone reached',
        timeAgo: '3d ago',
        color: AppTheme.successColor,
      ),
    ];
  }

  Color _getScoreColor(int score) {
    if (score >= 80) {
      return AppTheme.successColor;
    } else if (score >= 60) {
      return AppTheme.accentColor;
    } else {
      return AppTheme.warningColor;
    }
  }
}

class ActivityItem {
  final IconData icon;
  final String title;
  final String description;
  final String timeAgo;
  final Color color;
  final int? score;

  ActivityItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.timeAgo,
    required this.color,
    this.score,
  });
}
