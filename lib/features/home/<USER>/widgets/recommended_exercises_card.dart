import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../exercises/domain/entities/exercise.dart';
import '../../../exercises/presentation/bloc/exercises/exercises_bloc.dart';

class RecommendedExercisesCard extends StatelessWidget {
  const RecommendedExercisesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.psychology,
                      color: AppTheme.primaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Recommended Exercises',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        context.go(AppRouter.exercises);
                      },
                      child: ResponsiveText(
                        'View All',
                        style: AppTheme.labelMedium.copyWith(
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildLoadingState(),
                  loading: () => _buildLoadingState(),
                  loaded:
                      (
                        exercises,
                        filteredExercises,
                        categories,
                        currentFilter,
                        difficultyFilter,
                      ) => _buildExercisesList(context, exercises),
                  exerciseInProgress: (exercise) => _buildLoadingState(),
                  error: (message) => _buildErrorState(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Unable to load exercises',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExercisesList(BuildContext context, List<Exercise> exercises) {
    // Get top 3 recommended exercises
    final recommendedExercises = exercises.take(3).toList();

    return Column(
      children: recommendedExercises.map((exercise) {
        return Padding(
          padding: EdgeInsets.only(bottom: AppTheme.spacing12),
          child: _buildExerciseItem(context, exercise),
        );
      }).toList(),
    );
  }

  Widget _buildExerciseItem(BuildContext context, Exercise exercise) {
    return GestureDetector(
      onTap: () {
        _startExercise(context, exercise);
      },
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: _getExerciseColor(exercise.type).withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          border: Border.all(
            color: _getExerciseColor(exercise.type).withOpacity(0.2),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: _getExerciseColor(exercise.type).withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: Icon(
                _getExerciseIcon(exercise.type),
                color: _getExerciseColor(exercise.type),
                size: 24.sp,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    exercise.title,
                    style: AppTheme.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  ResponsiveText(
                    exercise.description,
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    runSpacing: 4.h,
                    children: [
                      _buildExerciseTag(
                        icon: Icons.timer,
                        text: '${exercise.estimatedDuration}min',
                        color: AppTheme.textTertiary,
                      ),
                      _buildExerciseTag(
                        icon: Icons.star,
                        text: _getDifficultyText(exercise.difficulty),
                        color: _getDifficultyColor(exercise.difficulty),
                      ),
                      if (exercise.lastScore != null)
                        _buildExerciseTag(
                          icon: Icons.trending_up,
                          text: '${exercise.lastScore}%',
                          color: AppTheme.successColor,
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.play_circle_filled,
              color: _getExerciseColor(exercise.type),
              size: 32.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12.sp),
          SizedBox(width: 2.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _startExercise(BuildContext context, Exercise exercise) {
    // Navigate to specific exercise based on type
    switch (exercise.id) {
      case 'word_recall':
        context.go(AppRouter.wordRecall);
        break;
      case 'number_sequence':
        context.go(AppRouter.numberSequence);
        break;
      case 'shape_matching':
        context.go(AppRouter.shapeMatching);
        break;
      case 'pattern_recognition':
        context.go(AppRouter.patternRecognition);
        break;
      case 'object_memory':
        context.go(AppRouter.objectMemory);
        break;
      default:
        // For other exercises, go to exercises page
        context.go(AppRouter.exercises);
        break;
    }
  }

  IconData _getExerciseIcon(String type) {
    switch (type) {
      case 'memory':
        return Icons.memory;
      case 'verbal':
        return Icons.record_voice_over;
      case 'pattern':
        return Icons.pattern;
      case 'logic':
        return Icons.calculate;
      case 'spatial':
        return Icons.rotate_right;
      default:
        return Icons.psychology;
    }
  }

  Color _getExerciseColor(String type) {
    switch (type) {
      case 'memory':
        return AppTheme.primaryColor;
      case 'verbal':
        return AppTheme.secondaryColor;
      case 'pattern':
        return AppTheme.accentColor;
      case 'logic':
        return AppTheme.warningColor;
      case 'spatial':
        return AppTheme.successColor;
      default:
        return AppTheme.primaryColor;
    }
  }

  String _getDifficultyText(int difficulty) {
    switch (difficulty) {
      case 1:
        return 'Easy';
      case 2:
        return 'Easy';
      case 3:
        return 'Medium';
      case 4:
        return 'Hard';
      case 5:
        return 'Expert';
      default:
        return 'Medium';
    }
  }

  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1:
      case 2:
        return AppTheme.successColor;
      case 3:
        return AppTheme.accentColor;
      case 4:
      case 5:
        return AppTheme.errorColor;
      default:
        return AppTheme.accentColor;
    }
  }
}
