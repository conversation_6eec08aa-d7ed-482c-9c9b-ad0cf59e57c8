import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:recallloop/features/home/<USER>/models/game_summary_stat_model.dart';
import 'package:recallloop/features/home/<USER>/bloc/home/<USER>/home_bloc_bloc.dart';

// import 'package:recallloop/features/home/<USER>/bloc/home_bloc_bloc.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

class QuickStatsCard extends StatelessWidget {
  const QuickStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBlocBloc, HomeBlocState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildLoadingState(),
                  loading: () => _buildLoadingState(),
                  loaded: (stats) => _buildGameStatsGrid(stats),
                  error: (_) => _buildErrorState(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 🔹 Header for the Card
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.analytics, color: AppTheme.primaryColor, size: 20.sp),
        SizedBox(width: AppTheme.spacing8),
        ResponsiveText(
          'Quick Stats',
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
      ],
    );
  }

  /// 🔹 Loading UI
  Widget _buildLoadingState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  /// 🔹 Error UI
  Widget _buildErrorState() {
    return SizedBox(
      height: 120.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'Unable to load stats',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameStatsGrid(GameSummaryStats stats) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.games,
                label: 'Assigned Games',
                value: stats.assignedGames,
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.play_arrow,
                label: 'Played Games',
                value: stats.playedGames,
                color: AppTheme.secondaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing12),
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                icon: Icons.score,
                label: 'Average Score',
                value: stats.avgScore,
                color: AppTheme.accentColor,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildStatItem(
                icon: Icons.stars,
                label: 'Reward Points',
                value: stats.rewardPoints,
                color: AppTheme.warningColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 🔹 Single Stat Item Widget
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    bool isLarge = true,
  }) {
    return Container(
      padding: EdgeInsets.all(
        isLarge ? AppTheme.spacing16 : AppTheme.spacing12,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(color: color.withOpacity(0.2), width: 1.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isLarge ? 8.w : 6.w),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: Icon(icon, color: color, size: isLarge ? 20.sp : 16.sp),
              ),
            ],
          ),
          SizedBox(height: isLarge ? AppTheme.spacing12 : AppTheme.spacing8),
          ResponsiveText(
            value,
            style: (isLarge ? AppTheme.headlineMedium : AppTheme.titleMedium)
                .copyWith(fontWeight: FontWeight.w700, color: color),
          ),
          SizedBox(height: 4.h),
          ResponsiveText(
            label,
            style: (isLarge ? AppTheme.bodySmall : AppTheme.labelSmall)
                .copyWith(color: AppTheme.textSecondary),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
