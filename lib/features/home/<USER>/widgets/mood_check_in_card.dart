import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../mood/presentation/bloc/mood/mood_bloc.dart';
import '../../../mood/domain/entities/mood_entry.dart';

class MoodCheckInCard extends StatelessWidget {
  const MoodCheckInCard({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MoodBloc, MoodState>(
      builder: (context, state) {
        return Card(
          child: Padding(
            padding: EdgeInsets.all(AppTheme.spacing20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.mood,
                      color: AppTheme.accentColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: AppTheme.spacing8),
                    ResponsiveText(
                      'Mood Check-in',
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        context.go(AppRouter.moodTracking);
                      },
                      child: ResponsiveText(
                        'View All',
                        style: AppTheme.labelMedium.copyWith(
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing16),
                state.when(
                  initial: () => _buildEmptyState(context),
                  loading: () => _buildLoadingState(),
                  loaded: (entries, stats) => _buildMoodContent(context, entries, stats),
                  entrySubmitting: () => _buildLoadingState(),
                  entrySubmitted: (entry) => _buildLoadingState(),
                  error: (message) => _buildEmptyState(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 100.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.accentColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 60.w,
          height: 60.h,
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          ),
          child: Icon(
            Icons.mood_outlined,
            color: AppTheme.accentColor,
            size: 30.sp,
          ),
        ),
        SizedBox(height: AppTheme.spacing12),
        ResponsiveText(
          'How are you feeling today?',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          'Track your mood to better understand your cognitive patterns',
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        _buildQuickMoodSelector(context),
      ],
    );
  }

  Widget _buildMoodContent(BuildContext context, List<MoodEntryModel> entries, MoodStats stats) {
    final lastEntry = entries.isNotEmpty ? entries.first : null;
    final averageMood = stats.averageMood;

    return Column(
      children: [
        if (lastEntry != null) ...[
          _buildLastMoodEntry(lastEntry),
          SizedBox(height: AppTheme.spacing16),
        ],
        Row(
          children: [
            Expanded(
              child: _buildMoodStat(
                label: 'Average Mood',
                value: averageMood.toStringAsFixed(1),
                icon: Icons.trending_up,
                color: _getMoodColor(averageMood.round()),
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: _buildMoodStat(
                label: 'Total Entries',
                value: '${stats.totalEntries}',
                icon: Icons.calendar_today,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: AppTheme.spacing16),
        _buildQuickMoodSelector(context),
      ],
    );
  }

  Widget _buildLastMoodEntry(MoodEntryModel entry) {
    final timeAgo = _getTimeAgo(entry.timestamp);
    
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: _getMoodColor(entry.moodLevel).withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: _getMoodColor(entry.moodLevel).withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: _getMoodColor(entry.moodLevel).withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
            ),
            child: Center(
              child: ResponsiveText(
                _getMoodEmoji(entry.moodLevel),
                style: TextStyle(fontSize: 20.sp),
              ),
            ),
          ),
          SizedBox(width: AppTheme.spacing12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  'Last mood: ${_getMoodLabel(entry.moodLevel)}',
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimary,
                  ),
                ),
                ResponsiveText(
                  timeAgo,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                if (entry.note != null && entry.note!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  ResponsiveText(
                    entry.note!,
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMoodStat({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1.w,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
          SizedBox(height: AppTheme.spacing8),
          ResponsiveText(
            value,
            style: AppTheme.titleMedium.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          ResponsiveText(
            label,
            style: AppTheme.labelSmall.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickMoodSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Quick mood check:',
          style: AppTheme.labelMedium.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(5, (index) {
            final moodLevel = index + 1;
            return GestureDetector(
              onTap: () {
                context.read<MoodBloc>().add(
                  MoodEvent.entryAdded(moodLevel: moodLevel),
                );
              },
              child: Container(
                width: 44.w,
                height: 44.h,
                decoration: BoxDecoration(
                  color: _getMoodColor(moodLevel).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                  border: Border.all(
                    color: _getMoodColor(moodLevel).withOpacity(0.3),
                    width: 1.w,
                  ),
                ),
                child: Center(
                  child: ResponsiveText(
                    _getMoodEmoji(moodLevel),
                    style: TextStyle(fontSize: 20.sp),
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  String _getMoodEmoji(int moodLevel) {
    switch (moodLevel) {
      case 1: return '😢';
      case 2: return '😕';
      case 3: return '😐';
      case 4: return '😊';
      case 5: return '😄';
      default: return '😐';
    }
  }

  String _getMoodLabel(int moodLevel) {
    switch (moodLevel) {
      case 1: return 'Very Bad';
      case 2: return 'Bad';
      case 3: return 'Neutral';
      case 4: return 'Good';
      case 5: return 'Excellent';
      default: return 'Unknown';
    }
  }

  Color _getMoodColor(int moodLevel) {
    switch (moodLevel) {
      case 1: return AppTheme.errorColor;
      case 2: return AppTheme.warningColor;
      case 3: return AppTheme.textTertiary;
      case 4: return AppTheme.secondaryColor;
      case 5: return AppTheme.successColor;
      default: return AppTheme.textTertiary;
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
