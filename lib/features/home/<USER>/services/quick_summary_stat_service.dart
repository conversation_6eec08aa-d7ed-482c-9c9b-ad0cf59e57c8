import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';
import 'package:recallloop/features/home/<USER>/models/game_summary_stat_model.dart';

class GameSummaryStatsService {
  static Future<GameSummaryStats?> getGameSummaryStats({
    required String tenantCode,
    required String userId,
    required String accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.gameSummaryStats}',
      );

      final response = await http
          .post(
            url,
            headers: {
              'Content-Type': 'application/json',
              'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
              'Authorization': 'Bearer $accessToken',
            },
            body: jsonEncode({'tenant_code': tenantCode, 'user_id': userId}),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        return GameSummaryStats.fromApiResponse(decoded);
      } else {
        debugPrint(
          'GameSummaryStats API error: ${response.statusCode} - ${response.body}',
        );
        return null;
      }
    } catch (e) {
      debugPrint('GameSummaryStats Exception: $e');
      return null;
    }
  }
}
