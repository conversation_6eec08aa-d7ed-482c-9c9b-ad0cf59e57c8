import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/exercises/data/models/assigned_game_model.dart';
import 'package:recallloop/features/exercises/data/services/listall_assignedgames_service.dart';
import 'package:recallloop/features/home/<USER>/bloc/home/<USER>/home_bloc_bloc.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../exercises/presentation/bloc/exercises/exercises_bloc.dart';
// import '../../../home/<USER>/bloc/home_bloc_bloc.dart';
import '../../../mood/presentation/bloc/mood/mood_bloc.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../widgets/assigned_games_card.dart';
import '../widgets/home_header.dart';
import '../widgets/mood_check_in_card.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/recent_activities_card.dart';
import '../widgets/recommended_exercises_card.dart';

class HomePageWrapper extends StatelessWidget {
  const HomePageWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => ProfileBloc()..add(const ProfileEvent.loadRequested()),
        ),
        BlocProvider(
          create: (_) =>
              ExercisesBloc()..add(const ExercisesEvent.loadRequested()),
        ),
        BlocProvider(
          create: (_) => MoodBloc()..add(const MoodEvent.loadRequested()),
        ),
        BlocProvider(create: (_) => HomeBlocBloc()), // For QuickStatsCard
      ],
      child: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String? displayName;
  bool isLoadingGames = false;
  List<AssignedGameInfo>? assignedGames;

  @override
  void initState() {
    super.initState();
    _loadData();
    _loadQuickStats();
  }

  Future<void> _loadQuickStats() async {
    final hiveUserService = HiveUserService();
    final userInfo = await hiveUserService.getUserInfo();
    final currentUser = await hiveUserService.getCurrentUser();

    final tenantCode = userInfo?['tenant_code'] ?? currentUser?.tenantCode;
    final userId = userInfo?['id'] ?? currentUser?.partyId;
    final accessToken = currentUser?.accessToken;

    if (tenantCode != null && userId != null && mounted) {
      context.read<HomeBlocBloc>().add(
        HomeBlocEvent.fetchGameSummaryStats(
          tenantCode: tenantCode,
          userId: userId,
          accessToken: accessToken ?? '',
        ),
      );
    }
  }

  Future<void> _loadData() async {
    setState(() => isLoadingGames = true);

    try {
      final hiveUserService = HiveUserService();
      final userInfo = await hiveUserService.getUserInfo();
      final currentUser = await hiveUserService.getCurrentUser();
      final accessToken = currentUser?.accessToken;

      final tenantCode = userInfo?['tenant_code'] ?? currentUser?.tenantCode;
      final partyId = userInfo?['id'] ?? currentUser?.partyId;
      final partyTypeKey =
          userInfo?['party_type_key'] ?? currentUser?.partyTypeKey;

      if (tenantCode != null && partyId != null && partyTypeKey != null) {
        final games = await AssignedGamesInfoService.getAssignedGames(
          tenantCode: tenantCode,
          partyId: partyId,
          partyTypeKey: partyTypeKey,
          accessToken: accessToken,
        );

        setState(() {
          assignedGames = games ?? [];
          isLoadingGames = false;
        });
      } else {
        setState(() {
          assignedGames = [];
          isLoadingGames = false;
        });
      }
    } catch (e) {
      setState(() {
        assignedGames = [];
        isLoadingGames = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load assigned games: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: const QuickActionButton(),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() => SafeArea(
    child: RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const HomeHeader(),
            SizedBox(height: AppTheme.spacing24),
            const QuickStatsCard(),
            SizedBox(height: AppTheme.spacing16),
            const RecommendedExercisesCard(),
            SizedBox(height: AppTheme.spacing16),
            _buildAssignedGames(),
            SizedBox(height: AppTheme.spacing16),
            const MoodCheckInCard(),
            SizedBox(height: AppTheme.spacing16),
            const RecentActivitiesCard(),
            SizedBox(height: AppTheme.spacing24),
          ],
        ),
      ),
    ),
  );

  Widget _buildTabletLayout() => SafeArea(
    child: RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const HomeHeader(),
            SizedBox(height: AppTheme.spacing32),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      const QuickStatsCard(),
                      SizedBox(height: AppTheme.spacing16),
                      const RecommendedExercisesCard(),
                      SizedBox(height: AppTheme.spacing16),
                      _buildAssignedGames(),
                    ],
                  ),
                ),
                SizedBox(width: AppTheme.spacing16),
                Expanded(
                  child: Column(
                    children: [
                      const MoodCheckInCard(),
                      SizedBox(height: AppTheme.spacing16),
                      const RecentActivitiesCard(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );

  Widget _buildDesktopLayout() => SafeArea(
    child: RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        child: ResponsiveContainer(
          maxWidth: 1200.w,
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeHeader(),
              SizedBox(height: AppTheme.spacing40),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      children: [
                        const QuickStatsCard(),
                        SizedBox(height: AppTheme.spacing24),
                        const RecommendedExercisesCard(),
                        SizedBox(height: AppTheme.spacing24),
                        _buildAssignedGames(),
                      ],
                    ),
                  ),
                  SizedBox(width: AppTheme.spacing24),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        const MoodCheckInCard(),
                        SizedBox(height: AppTheme.spacing24),
                        const RecentActivitiesCard(),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ),
  );

  Widget _buildAssignedGames() {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        final isLoading = state.maybeWhen(
          loading: () => true,
          orElse: () => false,
        );
        final games = state.maybeWhen(
          loaded: (exercises, _, __, ___, ____) => exercises
              .map(
                (exercise) => AssignedGameInfo(
                  gameId: exercise.id,
                  gameName: exercise.title,
                  categoryName: exercise.categoryName,
                  thumbnailUrl: exercise.iconPath,
                  description: exercise.description,
                  isActive: true,
                  noOfLevels: 1,
                ),
              )
              .toList(),
          orElse: () => <AssignedGameInfo>[],
        );
        return AssignedGamesCard(assignedGames: games, isLoading: isLoading);
      },
    );
  }
}

// 🔹 Floating Quick Action Button
class QuickActionButton extends StatelessWidget {
  const QuickActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickActionSheet(context),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: AppTheme.textOnPrimary,
      icon: const Icon(Icons.add),
      label: Text(
        'Quick Start',
        style: AppTheme.labelMedium.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }

  void _showQuickActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusLarge),
        ),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTheme.titleLarge.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppTheme.spacing16),
            _buildQuickActionItem(
              context,
              Icons.psychology,
              'Start Exercise',
              'Begin a cognitive exercise',
              AppRouter.exercises,
            ),
            _buildQuickActionItem(
              context,
              Icons.mood,
              'Log Mood',
              'Track your current mood',
              AppRouter.moodTracking,
            ),
            _buildQuickActionItem(
              context,
              Icons.analytics,
              'View Progress',
              'Check your performance',
              AppRouter.progress,
            ),
            SizedBox(height: AppTheme.spacing16),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    String route,
  ) {
    return ListTile(
      leading: Container(
        width: 48.w,
        height: 48.h,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: Icon(icon, color: AppTheme.primaryColor, size: 24.sp),
      ),
      title: Text(
        title,
        style: AppTheme.titleMedium.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
      ),
      onTap: () {
        Navigator.pop(context);
        context.go(route);
      },
      contentPadding: EdgeInsets.zero,
    );
  }
}
