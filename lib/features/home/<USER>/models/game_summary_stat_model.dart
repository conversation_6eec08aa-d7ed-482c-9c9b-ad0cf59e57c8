class GameSummaryStats {
  final String assignedGames;
  final String playedGames;
  final String avgScore;
  final String rewardPoints;

  GameSummaryStats({
    required this.assignedGames,
    required this.playedGames,
    required this.avgScore,
    required this.rewardPoints,
  });

  factory GameSummaryStats.fromApiResponse(Map<String, dynamic> json) {
    final List<dynamic> data = json['data'] ?? [];

    // Safely extract values by index
    return GameSummaryStats(
      assignedGames: data.isNotEmpty ? data[0]['title'] ?? '0' : '0',
      playedGames: data.length > 1 ? data[1]['title'] ?? '0' : '0',
      avgScore: data.length > 2 ? data[2]['title'] ?? '0' : '0',
      rewardPoints: data.length > 3 ? data[3]['title'] ?? '0' : '0',
    );
  }
}
