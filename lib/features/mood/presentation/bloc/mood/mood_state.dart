part of 'mood_bloc.dart';

@freezed
class MoodState with _$MoodState {
  const factory MoodState.initial() = _Initial;
  const factory MoodState.loading() = _Loading;
  const factory MoodState.loaded({
    required List<MoodEntryModel> entries,
    required MoodStats stats,
  }) = _Loaded;
  const factory MoodState.entrySubmitting() = _EntrySubmitting;
  const factory MoodState.entrySubmitted(MoodEntryModel entry) = _EntrySubmitted;
  const factory MoodState.error(String message) = _Error;
}
