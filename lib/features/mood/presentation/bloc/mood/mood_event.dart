part of 'mood_bloc.dart';

@freezed
class MoodEvent with _$MoodEvent {
  const factory MoodEvent.loadRequested() = _LoadRequested;
  const factory MoodEvent.entryAdded({
    required int moodLevel,
    String? note,
    @Default([]) List<String> tags,
    String? activityBefore,
  }) = _EntryAdded;
  const factory MoodEvent.entryUpdated({
    required String entryId,
    int? moodLevel,
    String? note,
    List<String>? tags,
    String? activityBefore,
  }) = _EntryUpdated;
  const factory MoodEvent.entryDeleted(String entryId) = _EntryDeleted;
  const factory MoodEvent.statsRequested({
    DateTime? startDate,
    DateTime? endDate,
  }) = _StatsRequested;
}
