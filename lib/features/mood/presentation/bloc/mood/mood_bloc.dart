import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/mood_entry.dart';

part 'mood_bloc.freezed.dart';
part 'mood_event.dart';
part 'mood_state.dart';

class MoodBloc extends Bloc<MoodEvent, MoodState> {
  List<MoodEntryModel> _entries = [];

  MoodBloc() : super(const _Initial()) {
    on<MoodEvent>((event, emit) async {
      try {
        if (event is _LoadRequested) {
          emit(const _Loading());
          
          // Simulate API call delay
          await Future.delayed(const Duration(seconds: 1));
          
          // Mock mood entries data
          _entries = [
            MoodEntryModel(
              id: '1',
              moodLevel: 4,
              note: 'Feeling great after morning exercise!',
              timestamp: DateTime.now().subtract(const Duration(hours: 2)),
              tags: const ['energetic', 'happy'],
              activityBefore: 'Exercise',
            ),
            MoodEntryModel(
              id: '2',
              moodLevel: 3,
              note: 'Neutral mood, just okay',
              timestamp: DateTime.now().subtract(const Duration(days: 1)),
              tags: const ['neutral'],
              activityBefore: 'Work',
            ),
            MoodEntryModel(
              id: '3',
              moodLevel: 5,
              note: 'Excellent mood! Had a great day with friends',
              timestamp: DateTime.now().subtract(const Duration(days: 2)),
              tags: const ['excited', 'social', 'happy'],
              activityBefore: 'Social activity',
            ),
            MoodEntryModel(
              id: '4',
              moodLevel: 2,
              note: 'Feeling a bit down today',
              timestamp: DateTime.now().subtract(const Duration(days: 3)),
              tags: const ['sad', 'tired'],
              activityBefore: 'Work',
            ),
            MoodEntryModel(
              id: '5',
              moodLevel: 4,
              timestamp: DateTime.now().subtract(const Duration(days: 4)),
              tags: const ['content'],
              activityBefore: 'Reading',
            ),
          ];
          
          final stats = _calculateStats(_entries);
          emit(_Loaded(entries: _entries, stats: stats));
        }
        
        if (event is _EntryAdded) {
          emit(const _EntrySubmitting());
          
          // Simulate API call delay
          await Future.delayed(const Duration(seconds: 1));
          
          final newEntry = MoodEntryModel(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            moodLevel: event.moodLevel,
            note: event.note,
            timestamp: DateTime.now(),
            tags: event.tags,
            activityBefore: event.activityBefore,
          );
          
          _entries.insert(0, newEntry); // Add to beginning for chronological order
          
          emit(_EntrySubmitted(newEntry));
          
          // Reload with updated stats
          final stats = _calculateStats(_entries);
          emit(_Loaded(entries: _entries, stats: stats));
        }
        
        if (event is _EntryUpdated) {
          emit(const _Loading());
          
          // Simulate API call delay
          await Future.delayed(const Duration(milliseconds: 500));
          
          final entryIndex = _entries.indexWhere((entry) => entry.id == event.entryId);
          if (entryIndex != -1) {
            final updatedEntry = _entries[entryIndex].copyWith(
              moodLevel: event.moodLevel ?? _entries[entryIndex].moodLevel,
              note: event.note ?? _entries[entryIndex].note,
              tags: event.tags ?? _entries[entryIndex].tags,
              activityBefore: event.activityBefore ?? _entries[entryIndex].activityBefore,
            );
            
            _entries[entryIndex] = updatedEntry;
            
            final stats = _calculateStats(_entries);
            emit(_Loaded(entries: _entries, stats: stats));
          } else {
            emit(const _Error('Mood entry not found'));
          }
        }
        
        if (event is _EntryDeleted) {
          emit(const _Loading());
          
          // Simulate API call delay
          await Future.delayed(const Duration(milliseconds: 500));
          
          _entries.removeWhere((entry) => entry.id == event.entryId);
          
          final stats = _calculateStats(_entries);
          emit(_Loaded(entries: _entries, stats: stats));
        }
        
        if (event is _StatsRequested) {
          if (state is _Loaded) {
            final currentState = state as _Loaded;
            
            List<MoodEntryModel> filteredEntries = currentState.entries;
            
            if (event.startDate != null || event.endDate != null) {
              filteredEntries = currentState.entries.where((entry) {
                if (event.startDate != null && entry.timestamp.isBefore(event.startDate!)) {
                  return false;
                }
                if (event.endDate != null && entry.timestamp.isAfter(event.endDate!)) {
                  return false;
                }
                return true;
              }).toList();
            }
            
            final stats = _calculateStats(filteredEntries);
            emit(_Loaded(entries: currentState.entries, stats: stats));
          }
        }
      } on Exception catch (e) {
        emit(_Error(e.toString()));
      }
    });
  }

  MoodStats _calculateStats(List<MoodEntryModel> entries) {
    if (entries.isEmpty) {
      return const MoodStats(
        averageMood: 0.0,
        totalEntries: 0,
        moodDistribution: {},
        commonTags: [],
      );
    }

    final totalMood = entries.fold<int>(0, (sum, entry) => sum + entry.moodLevel);
    final averageMood = totalMood / entries.length;

    final moodDistribution = <int, int>{};
    final allTags = <String>[];

    for (final entry in entries) {
      moodDistribution[entry.moodLevel] = (moodDistribution[entry.moodLevel] ?? 0) + 1;
      allTags.addAll(entry.tags);
    }

    // Get most common tags
    final tagCounts = <String, int>{};
    for (final tag in allTags) {
      tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
    }

    final commonTags = tagCounts.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));

    return MoodStats(
      averageMood: averageMood,
      totalEntries: entries.length,
      moodDistribution: moodDistribution,
      commonTags: commonTags.take(5).map((e) => e.key).toList(),
      lastEntryDate: entries.isNotEmpty ? entries.first.timestamp : null,
    );
  }
}
