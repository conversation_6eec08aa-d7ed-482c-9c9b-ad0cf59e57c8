import 'package:freezed_annotation/freezed_annotation.dart';

part 'mood_entry.freezed.dart';

@freezed
class MoodEntryModel with _$MoodEntryModel {
  const factory MoodEntryModel({
    required String id,
    required int moodLevel, // 1-5 scale
    String? note,
    required DateTime timestamp,
    @Default([]) List<String> tags,
    String? activityBefore,
  }) = _MoodEntryModel;
}

@freezed
class MoodStats with _$MoodStats {
  const factory MoodStats({
    required double averageMood,
    required int totalEntries,
    required Map<int, int> moodDistribution,
    required List<String> commonTags,
    DateTime? lastEntryDate,
  }) = _MoodStats;
}
