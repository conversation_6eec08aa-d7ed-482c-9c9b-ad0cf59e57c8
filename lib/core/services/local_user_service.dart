import '../database/database_helper.dart';
import '../database/entities/local_user_entity.dart';

class LocalUserService {
  static final LocalUserService _instance = LocalUserService._internal();
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  LocalUserService._internal();

  factory LocalUserService() => _instance;

  /// Save user data to local database after successful login
  Future<bool> saveUserAfterLogin(dynamic loginModel) async {
    try {
      print('Attempting to save user after login. LoginModel:');
      print(loginModel);
      final localUser = LocalUserEntity.fromLoginModel(loginModel);
      print('Converted LocalUserEntity:');
      print(localUser);

      // Check if user already exists
      final existingUser = await _databaseHelper.getUserById(localUser.id);
      print('Existing user: $existingUser');

      if (existingUser != null) {
        // Update existing user with new login data
        final updatedUser = localUser.copyWith(
          localCreatedAt:
              existingUser.localCreatedAt, // Keep original creation time
          localUpdatedAt: DateTime.now(),
        );
        await _databaseHelper.updateUser(updatedUser);
        print('Updated existing user in DB.');
      } else {
        // Insert new user
        await _databaseHelper.insertUser(localUser);
        print('Inserted new user in DB.');
      }

      return true;
    } catch (e) {
      print('Error saving user after login: $e');
      return false;
    }
  }

  /// Check if user is logged in and token is still valid
  Future<bool> isLoggedIn() async {
    try {
      // Get all users (assuming single user app, get the most recent one)
      final users = await _databaseHelper.getAllUsers();
      print('Loaded users from DB: $users');

      if (users.isEmpty) {
        print('No users found in DB.');
        return false;
      }

      // Get the most recent user (first in the list due to ORDER BY local_created_at DESC)
      final user = users.first;
      print('Most recent user: $user');

      // Check if token is still valid (not expired)
      final now = DateTime.now();
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        user.expiresAt * 1000,
      );
      print('Token expiresAt: $expiresAt, now: $now');

      if (now.isAfter(expiresAt)) {
        // Token has expired
        print('Token has expired.');
        return false;
      }

      // User is logged in and token is valid
      print('User is logged in and token is valid.');
      return true;
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  /// Logout user by clearing all user data
  Future<bool> logout() async {
    try {
      await _databaseHelper.deleteAllUsers();
      return true;
    } catch (e) {
      print('Error during logout: $e');
      return false;
    }
  }

  /// Save user data to local database after successful signup
  Future<bool> saveUserAfterSignup(dynamic signupModel) async {
    try {
      final localUser = LocalUserEntity.fromSignupModel(signupModel);

      // Check if user already exists
      final existingUser = await _databaseHelper.getUserById(localUser.id);

      if (existingUser != null) {
        // Update existing user with new signup data
        final updatedUser = localUser.copyWith(
          localCreatedAt:
              existingUser.localCreatedAt, // Keep original creation time
          localUpdatedAt: DateTime.now(),
        );
        await _databaseHelper.updateUser(updatedUser);
      } else {
        // Insert new user
        await _databaseHelper.insertUser(localUser);
      }

      return true;
    } catch (e) {
      print('Error saving user after signup: $e');
      return false;
    }
  }

  /// Get current logged-in user from local database (with token validation)
  Future<LocalUserEntity?> getCurrentUser() async {
    try {
      final users = await _databaseHelper.getAllUsers();
      if (users.isNotEmpty) {
        // Return the most recently updated user (assuming single user login)
        users.sort((a, b) => b.localUpdatedAt.compareTo(a.localUpdatedAt));
        final user = users.first;

        // Check if token is still valid
        final now = DateTime.now();
        final expiresAt = DateTime.fromMillisecondsSinceEpoch(
          user.expiresAt * 1000,
        );

        if (now.isAfter(expiresAt)) {
          // Token has expired, return null
          return null;
        }

        return user;
      }
      return null;
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  /// Get user by ID
  Future<LocalUserEntity?> getUserById(String id) async {
    try {
      return await _databaseHelper.getUserById(id);
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  /// Get user by email
  Future<LocalUserEntity?> getUserByEmail(String email) async {
    try {
      return await _databaseHelper.getUserByEmail(email);
    } catch (e) {
      print('Error getting user by email: $e');
      return null;
    }
  }

  /// Get user by username
  Future<LocalUserEntity?> getUserByUsername(String username) async {
    try {
      return await _databaseHelper.getUserByUsername(username);
    } catch (e) {
      print('Error getting user by username: $e');
      return null;
    }
  }

  /// Update user information
  Future<bool> updateUser(LocalUserEntity user) async {
    try {
      final result = await _databaseHelper.updateUser(user);
      return result > 0;
    } catch (e) {
      print('Error updating user: $e');
      return false;
    }
  }

  /// Check if user is logged in (has valid token)
  Future<bool> isUserLoggedIn() async {
    try {
      final user = await getCurrentUser();
      if (user == null) return false;

      // Check if token is still valid (not expired)
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return user.expiresAt > now;
    } catch (e) {
      print('Error checking if user is logged in: $e');
      return false;
    }
  }

  /// Get user's access token
  Future<String?> getAccessToken() async {
    try {
      final user = await getCurrentUser();
      if (user != null && await isUserLoggedIn()) {
        return user.accessToken;
      }
      return null;
    } catch (e) {
      print('Error getting access token: $e');
      return null;
    }
  }

  /// Get user's refresh token
  Future<String?> getRefreshToken() async {
    try {
      final user = await getCurrentUser();
      return user?.refreshToken;
    } catch (e) {
      print('Error getting refresh token: $e');
      return null;
    }
  }

  /// Update user tokens (for token refresh)
  Future<bool> updateUserTokens({
    required String userId,
    required String accessToken,
    required String refreshToken,
    required int expiresIn,
    required int expiresAt,
  }) async {
    try {
      final user = await _databaseHelper.getUserById(userId);
      if (user == null) return false;

      final updatedUser = user.copyWith(
        accessToken: accessToken,
        refreshToken: refreshToken,
        expiresIn: expiresIn,
        expiresAt: expiresAt,
        localUpdatedAt: DateTime.now(),
      );

      final result = await _databaseHelper.updateUser(updatedUser);
      return result > 0;
    } catch (e) {
      print('Error updating user tokens: $e');
      return false;
    }
  }

  /// Logout user (clear local data)
  Future<bool> logoutUser() async {
    try {
      await _databaseHelper.deleteAllUsers();
      return true;
    } catch (e) {
      print('Error logging out user: $e');
      return false;
    }
  }

  /// Delete specific user
  Future<bool> deleteUser(String userId) async {
    try {
      final result = await _databaseHelper.deleteUser(userId);
      return result > 0;
    } catch (e) {
      print('Error deleting user: $e');
      return false;
    }
  }

  /// Get all users (for debugging or admin purposes)
  Future<List<LocalUserEntity>> getAllUsers() async {
    try {
      return await _databaseHelper.getAllUsers();
    } catch (e) {
      print('Error getting all users: $e');
      return [];
    }
  }

  /// Check if user exists in local database
  Future<bool> userExists(String userId) async {
    try {
      return await _databaseHelper.userExists(userId);
    } catch (e) {
      print('Error checking if user exists: $e');
      return false;
    }
  }

  /// Get total user count
  Future<int> getUserCount() async {
    try {
      return await _databaseHelper.getUserCount();
    } catch (e) {
      print('Error getting user count: $e');
      return 0;
    }
  }

  /// Clear all user data (for app reset)
  Future<bool> clearAllUserData() async {
    try {
      await _databaseHelper.deleteAllUsers();
      return true;
    } catch (e) {
      print('Error clearing all user data: $e');
      return false;
    }
  }
}
