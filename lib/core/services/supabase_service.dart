import 'package:supabase_flutter/supabase_flutter.dart';

/// Utility class to access Supabase client throughout the app
class SupabaseService {
  /// Get the Supabase client instance
  static SupabaseClient get client => Supabase.instance.client;
  
  /// Get the current user
  static User? get currentUser => client.auth.currentUser;
  
  /// Check if user is authenticated
  static bool get isAuthenticated => currentUser != null;
}
