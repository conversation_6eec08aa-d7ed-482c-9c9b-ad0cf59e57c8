class GameConstants {
  // Word Recall Data
  static const Map<String, List<String>> wordRecallData = {
    "easy": [
      "cat",
      "dog",
      "sun",
      "car",
      "book",
      "tree",
      "ball",
      "fish",
      "bird",
      "cake",
      "door",
      "hand",
      "moon",
      "star",
      "fire",
      "love",
      "home",
      "blue",
      "red",
      "big",
      "run",
      "jump",
      "walk",
      "talk",
      "play",
      "work",
      "food",
      "milk",
      "egg",
      "cup",
      "pen",
      "box",
      "hat",
      "bag",
      "key",
      "bed",
      "eye",
      "ear",
      "arm",
      "leg",
      "hot",
      "cold",
      "new",
      "old",
      "good",
      "bad",
      "fast",
      "slow",
      "up",
      "down"
    ],
    "medium": [
      "apple",
      "house",
      "water",
      "green",
      "happy",
      "music",
      "chair",
      "table",
      "phone",
      "light",
      "paper",
      "money",
      "smile",
      "dance",
      "laugh",
      "write",
      "learn",
      "teach",
      "friend",
      "family",
      "school",
      "garden",
      "flower",
      "animal",
      "planet",
      "ocean",
      "mountain",
      "forest",
      "bridge",
      "window",
      "picture",
      "camera",
      "guitar",
      "piano",
      "orange",
      "purple",
      "yellow",
      "silver",
      "golden",
      "bright",
      "strong",
      "gentle",
      "winter",
      "summer",
      "spring",
      "autumn",
      "morning",
      "evening",
      "travel",
      "journey"
    ],
    "hard": [
      "elephant",
      "butterfly",
      "computer",
      "telephone",
      "television",
      "refrigerator",
      "beautiful",
      "wonderful",
      "fantastic",
      "incredible",
      "magnificent",
      "spectacular",
      "adventure",
      "discovery",
      "knowledge",
      "education",
      "creativity",
      "imagination",
      "philosophy",
      "psychology",
      "technology",
      "architecture",
      "engineering",
      "mathematics",
      "democracy",
      "government",
      "community",
      "environment",
      "atmosphere",
      "temperature"
    ]
  };

  // Shape Pattern Data
  static const List<Map<String, dynamic>> shapePatternData = [
    {
      "sequence": ["circle", "square", "circle", "square", "?"],
      "options": ["circle", "square", "triangle", "star"],
      "answer": "circle",
      "explanation": "Alternates between circle and square."
    },
    {
      "sequence": ["circle", "square", "triangle", "circle", "square", "?"],
      "options": ["triangle", "circle", "square", "diamond"],
      "answer": "triangle",
      "explanation": "Rotates through circle, square, triangle."
    },
    {
      "sequence": ["triangle", "square", "pentagon", "hexagon", "?"],
      "options": ["heptagon", "octagon", "triangle", "square"],
      "answer": "heptagon",
      "explanation": "Each shape has one more side than the previous."
    },
    {
      "sequence": ["star", "star", "diamond", "diamond", "?"],
      "options": ["star", "diamond", "circle", "square"],
      "answer": "star",
      "explanation": "Each shape repeats twice, then switches."
    },
    {
      "sequence": ["circle", "square", "triangle", "diamond", "?"],
      "options": ["star", "circle", "square", "triangle"],
      "answer": "star",
      "explanation": "Each shape is unique, next is star."
    },
    {
      "sequence": ["pentagon", "hexagon", "heptagon", "octagon", "?"],
      "options": ["nonagon", "pentagon", "hexagon", "octagon"],
      "answer": "nonagon",
      "explanation": "Each shape has one more side than the previous."
    },
    {
      "sequence": ["triangle", "triangle", "square", "square", "?"],
      "options": ["triangle", "square", "circle", "diamond"],
      "answer": "triangle",
      "explanation": "Each shape repeats twice, then switches."
    },
    {
      "sequence": ["circle", "diamond", "circle", "diamond", "?"],
      "options": ["circle", "diamond", "star", "triangle"],
      "answer": "circle",
      "explanation": "Alternates between circle and diamond."
    }
  ];

  // Letter Pattern Data
  static const List<Map<String, dynamic>> letterPatternData = [
    {
      "sequence": ["A", "C", "E", "G", "?"],
      "options": ["I", "H", "F", "D"],
      "answer": "I",
      "explanation": "Skip one letter each time: A → C → E → G → I"
    },
    {
      "sequence": ["A", "B", "C", "D", "?"],
      "options": ["E", "F", "G", "H"],
      "answer": "E",
      "explanation": "Next letter in alphabet: A → B → C → D → E"
    },
    {
      "sequence": ["A", "E", "I", "O", "?"],
      "options": ["U", "Y", "A", "E"],
      "answer": "U",
      "explanation": "Vowels in order: A → E → I → O → U"
    },
    {
      "sequence": ["A", "A", "B", "B", "?"],
      "options": ["C", "D", "A", "E"],
      "answer": "C",
      "explanation": "Repeat each letter twice: AA → BB → CC"
    }
  ];

  // Number Pattern Data
  static const List<Map<String, dynamic>> numberPatternData = [
    {
      "sequence": [2, 4, 6, 8, "?"],
      "options": [10, 12, 9, 16],
      "answer": 10,
      "explanation": "Add 2 to each number."
    },
    {
      "sequence": [1, 2, 4, 8, "?"],
      "options": [16, 12, 20, 24],
      "answer": 16,
      "explanation": "Multiply by 2 each time."
    },
    {
      "sequence": [10, 8, 6, 4, "?"],
      "options": [2, 3, 5, 1],
      "answer": 2,
      "explanation": "Subtract 2 each time."
    },
    {
      "sequence": [1, 4, 9, 16, "?"],
      "options": [25, 20, 30, 35],
      "answer": 25,
      "explanation": "Square numbers: 1², 2², 3², 4², 5²"
    },
    {
      "sequence": [1, 1, 2, 3, "?"],
      "options": [5, 4, 6, 7],
      "answer": 5,
      "explanation": "Fibonacci: Add previous two numbers."
    },
    {
      "sequence": [1, 4, 7, 10, "?"],
      "options": [13, 12, 14, 15],
      "answer": 13,
      "explanation": "Add 3 each time."
    },
    {
      "sequence": [1, 3, 9, 27, "?"],
      "options": [81, 54, 72, 90],
      "answer": 81,
      "explanation": "Powers of 3: 3⁰, 3¹, 3², 3³, 3⁴"
    },
    {
      "sequence": [5, 8, 6, 9, "?"],
      "options": [7, 12, 6, 10],
      "answer": 7,
      "explanation": "Alternating pattern: +3, -2"
    },
    {
      "sequence": [1, 3, 7, 15, "?"],
      "options": [31, 30, 29, 32],
      "answer": 31,
      "explanation": "Double and add 1 each time."
    },
    {
      "sequence": [20, 19, 17, 14, "?"],
      "options": [10, 11, 9, 12],
      "answer": 10,
      "explanation": "Subtract 1, 2, 3, 4..."
    }
  ];
} 