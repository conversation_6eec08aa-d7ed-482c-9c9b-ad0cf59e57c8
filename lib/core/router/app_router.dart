import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/onboarding_page.dart';
import '../../features/auth/presentation/pages/signup_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/exercises/presentation/pages/exercises_page.dart';
import '../../features/exercises/presentation/pages/number_sequence_page.dart';
import '../../features/exercises/presentation/pages/object_memory_page.dart';
import '../../features/exercises/presentation/pages/pattern_recognition_page.dart';
import '../../features/exercises/presentation/pages/shape_matching_page.dart';
import '../../features/exercises/presentation/pages/word_recall_page.dart';
import '../../features/home/<USER>/pages/home_page.dart';
import '../../features/mood/presentation/pages/mood_tracking_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/progress/presentation/pages/progress_page.dart';
import '../../shared/presentation/pages/main_navigation_page.dart';

class AppRouter {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String home = '/home';
  static const String exercises = '/exercises';

  static const String wordRecall = '/exercises/word-recall';
  static const String numberSequence = '/exercises/number-sequence';
  static const String shapeMatching = '/exercises/shape-matching';
  static const String patternRecognition = '/exercises/pattern-recognition';
  static const String objectMemory = '/exercises/object-memory';
  static const String profile = '/profile';
  static const String moodTracking = '/mood';
  static const String progress = '/progress';

  static final GoRouter router = GoRouter(
    initialLocation: splash,
    routes: [
      // Splash Screen Route
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Authentication Routes
      GoRoute(
        path: onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: signup,
        name: 'signup',
        builder: (context, state) => const SignupPage(),
      ),

      // Main App Routes with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainNavigationPage(child: child),
        routes: [
          GoRoute(
            path: home,
            name: 'home',
            builder: (context, state) => const HomePageWrapper(),
          ),
          GoRoute(
            path: exercises,
            name: 'exercises',
            builder: (context, state) => const ExercisesPage(),
          ),
          GoRoute(
            path: profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: moodTracking,
            name: 'mood',
            builder: (context, state) => const MoodTrackingPage(),
          ),
          GoRoute(
            path: progress,
            name: 'progress',
            builder: (context, state) => const ProgressPage(),
          ),
        ],
      ),

      // Exercise Detail Routes (outside shell for full-screen experience)
      GoRoute(
        path: wordRecall,
        name: 'word-recall',
        builder: (context, state) => const WordRecallPage(),
      ),
      GoRoute(
        path: numberSequence,
        name: 'number-sequence',
        builder: (context, state) => const NumberSequencePage(),
      ),
      GoRoute(
        path: shapeMatching,
        name: 'shape-matching',
        builder: (context, state) => const ShapeMatchingPage(),
      ),
      GoRoute(
        path: patternRecognition,
        name: 'pattern-recognition',
        builder: (context, state) => const PatternRecognitionPage(),
      ),
      GoRoute(
        path: objectMemory,
        name: 'object-memory',
        builder: (context, state) => const ObjectMemoryPage(),
      ),
    ],
  );
}
