import 'package:flutter/foundation.dart';
import '../services/local_user_service.dart';
import 'entities/local_user_entity.dart';

/// Test class to verify SQLite database functionality
/// This should only be used in development/debug mode
class DatabaseTester {
  static final LocalUserService _localUserService = LocalUserService();

  /// Test basic database operations
  static Future<void> testDatabaseOperations() async {
    if (!kDebugMode) {
      return;
    }

    try {
      // Test 1: Clear any existing data
      await _localUserService.clearAllUserData();
      final initialCount = await _localUserService.getUserCount();

      // Test 2: Create a mock user
      final mockUser = _createMockUser();

      // Test 3: Save user (simulate login)
      final mockLoginModel = _createMockLoginModel();
      final saveSuccess = await _localUserService.saveUserAfterLogin(
        mockLoginModel,
      );

      // Test 3b: Save user (simulate signup)
      final mockSignupModel = _createMockSignupModel();
      final signupSaveSuccess = await _localUserService.saveUserAfterSignup(
        mockSignupModel,
      );

      // Test 4: Retrieve user
      final retrievedUser = await _localUserService.getCurrentUser();
      if (retrievedUser == null) {
        throw Exception('Failed to retrieve user');
      }

      // Test 5: Check if user is logged in
      final isLoggedIn = await _localUserService.isUserLoggedIn();

      // Test 6: Get user by email
      final userByEmail = await _localUserService.getUserByEmail(
        '<EMAIL>',
      );

      // Test 7: Update user
      if (retrievedUser != null) {
        final updatedUser = retrievedUser.copyWith(
          firstName: 'Updated',
          lastName: 'Name',
        );
        final updateSuccess = await _localUserService.updateUser(updatedUser);

        // Verify update
        final updatedRetrievedUser = await _localUserService.getCurrentUser();
        if (updatedRetrievedUser == null) {
          throw Exception('Failed to retrieve updated user');
        }
      }

      // Test 8: Get all users
      final allUsers = await _localUserService.getAllUsers();

      // Test 9: User count
      final userCount = await _localUserService.getUserCount();

      // Test 10: Logout (clear data)
      final logoutSuccess = await _localUserService.logoutUser();
      final finalCount = await _localUserService.getUserCount();

      // Verify cleanup
      if (finalCount != 0) {
        throw Exception('Database cleanup failed');
      }
    } catch (e) {
      print('Database test failed: $e');
    }
  }

  /// Create a mock SignupModel for testing
  static dynamic _createMockSignupModel() {
    // This is a simplified mock object that mimics the structure of SignupModel
    return MockSignupModel(
      accessToken: 'mock_signup_access_token_789',
      tokenType: 'Bearer',
      expiresIn: 3600,
      expiresAt: (DateTime.now().millisecondsSinceEpoch ~/ 1000) + 3600,
      refreshToken: 'mock_signup_refresh_token_101',
      user: MockSignupUser(
        id: 'signup_user_456',
        aud: 'authenticated',
        role: 'authenticated',
        email: '<EMAIL>',
        emailConfirmedAt: DateTime.now().toIso8601String(),
        phone: '+**********',
        lastSignInAt: DateTime.now().toIso8601String(),
        appMetadata: MockAppMetadata(provider: 'email', providers: ['email']),
        userMetadata: MockSignupUserMetadata(
          email: '<EMAIL>',
          emailVerified: false, // Usually false for new signups
          phoneVerified: false,
          sub: 'signup_user_456',
        ),
        identities: [],
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
        isAnonymous: false,
      ),
    );
  }

  /// Create a mock LoginModel for testing
  static dynamic _createMockLoginModel() {
    // This is a simplified mock object that mimics the structure of LoginModel
    return MockLoginModel(
      accessToken: 'mock_access_token_123',
      tokenType: 'Bearer',
      expiresIn: 3600,
      expiresAt: (DateTime.now().millisecondsSinceEpoch ~/ 1000) + 3600,
      refreshToken: 'mock_refresh_token_456',
      user: MockUser(
        id: 'user_123',
        aud: 'authenticated',
        role: 'authenticated',
        email: '<EMAIL>',
        emailConfirmedAt: DateTime.now().toIso8601String(),
        phone: '+**********',
        confirmedAt: DateTime.now().toIso8601String(),
        lastSignInAt: DateTime.now().toIso8601String(),
        appMetadata: MockAppMetadata(provider: 'email', providers: ['email']),
        userMetadata: MockUserMetadata(
          avatarUrl: 'https://example.com/avatar.jpg',
          email: '<EMAIL>',
          emailVerified: true,
          firstName: 'Test',
          lastName: 'User',
          partyTypeKey: 'PLAYER',
          phone: '+**********',
          phoneVerified: true,
          sub: 'user_123',
          tenantCode: 'TN-0001',
          address: 'testuser',
        ),
        identities: [],
        createdAt: DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
        isAnonymous: false,
      ),
    );
  }

  /// Create a mock LocalUserEntity for testing
  static LocalUserEntity _createMockUser() {
    final now = DateTime.now();
    return LocalUserEntity(
      id: 'test_user_123',
      email: '<EMAIL>',
      phone: '+**********',
      firstName: 'Test',
      lastName: 'User',
      address: 'testuser',
      avatarUrl: 'https://example.com/avatar.jpg',
      tenantCode: 'TN-0001',
      partyTypeKey: 'PLAYER',
      emailVerified: true,
      phoneVerified: true,
      accessToken: 'mock_access_token',
      refreshToken: 'mock_refresh_token',
      tokenType: 'Bearer',
      expiresIn: 3600,
      expiresAt: (now.millisecondsSinceEpoch ~/ 1000) + 3600,
      role: 'authenticated',
      aud: 'authenticated',
      emailConfirmedAt: now.toIso8601String(),
      confirmedAt: now.toIso8601String(),
      lastSignInAt: now.toIso8601String(),
      createdAt: now.toIso8601String(),
      updatedAt: now.toIso8601String(),
      isAnonymous: false,
      localCreatedAt: now,
      localUpdatedAt: now,
    );
  }
}

// Mock classes for testing
class MockLoginModel {
  final String accessToken;
  final String tokenType;
  final int expiresIn;
  final int expiresAt;
  final String refreshToken;
  final MockUser user;

  MockLoginModel({
    required this.accessToken,
    required this.tokenType,
    required this.expiresIn,
    required this.expiresAt,
    required this.refreshToken,
    required this.user,
  });
}

class MockUser {
  final String id;
  final String aud;
  final String role;
  final String email;
  final String emailConfirmedAt;
  final String phone;
  final String confirmedAt;
  final String lastSignInAt;
  final MockAppMetadata appMetadata;
  final MockUserMetadata userMetadata;
  final List<dynamic> identities;
  final String createdAt;
  final String updatedAt;
  final bool isAnonymous;

  MockUser({
    required this.id,
    required this.aud,
    required this.role,
    required this.email,
    required this.emailConfirmedAt,
    required this.phone,
    required this.confirmedAt,
    required this.lastSignInAt,
    required this.appMetadata,
    required this.userMetadata,
    required this.identities,
    required this.createdAt,
    required this.updatedAt,
    required this.isAnonymous,
  });
}

class MockAppMetadata {
  final String provider;
  final List<String> providers;

  MockAppMetadata({required this.provider, required this.providers});
}

class MockUserMetadata {
  final String avatarUrl;
  final String email;
  final bool emailVerified;
  final String firstName;
  final String lastName;
  final String partyTypeKey;
  final String phone;
  final bool phoneVerified;
  final String sub;
  final String tenantCode;
  final String address;

  MockUserMetadata({
    required this.avatarUrl,
    required this.email,
    required this.emailVerified,
    required this.firstName,
    required this.lastName,
    required this.partyTypeKey,
    required this.phone,
    required this.phoneVerified,
    required this.sub,
    required this.tenantCode,
    required this.address,
  });
}

// Mock classes for SignupModel testing
class MockSignupModel {
  final String accessToken;
  final String tokenType;
  final int expiresIn;
  final int expiresAt;
  final String refreshToken;
  final MockSignupUser user;

  MockSignupModel({
    required this.accessToken,
    required this.tokenType,
    required this.expiresIn,
    required this.expiresAt,
    required this.refreshToken,
    required this.user,
  });
}

class MockSignupUser {
  final String id;
  final String aud;
  final String role;
  final String email;
  final String emailConfirmedAt;
  final String phone;
  final String lastSignInAt;
  final MockAppMetadata appMetadata;
  final MockSignupUserMetadata userMetadata;
  final List<dynamic> identities;
  final String createdAt;
  final String updatedAt;
  final bool isAnonymous;

  MockSignupUser({
    required this.id,
    required this.aud,
    required this.role,
    required this.email,
    required this.emailConfirmedAt,
    required this.phone,
    required this.lastSignInAt,
    required this.appMetadata,
    required this.userMetadata,
    required this.identities,
    required this.createdAt,
    required this.updatedAt,
    required this.isAnonymous,
  });
}

class MockSignupUserMetadata {
  final String email;
  final bool emailVerified;
  final bool phoneVerified;
  final String sub;

  MockSignupUserMetadata({
    required this.email,
    required this.emailVerified,
    required this.phoneVerified,
    required this.sub,
  });
}
