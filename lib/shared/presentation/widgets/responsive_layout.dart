import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200.w) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= 768.w) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}

class ResponsiveBreakpoints {
  static double get mobile => 768.w;
  static double get tablet => 1200.w;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < tablet;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tablet;
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? scaleFactor;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.scaleFactor,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double factor = scaleFactor ?? 1.0;
        
        // Adjust text scale based on screen size
        if (constraints.maxWidth < 360.w) {
          factor *= 0.9;
        } else if (constraints.maxWidth > 600.w) {
          factor *= 1.1;
        }

        return Text(
          text,
          style: style?.copyWith(
            fontSize: (style?.fontSize ?? 14.sp) * factor,
          ),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
          textScaleFactor: 1.0, // Prevent system text scaling from interfering
        );
      },
    );
  }
}

class ResponsivePadding extends StatelessWidget {
  final Widget child;
  final EdgeInsets? mobile;
  final EdgeInsets? tablet;
  final EdgeInsets? desktop;

  const ResponsivePadding({
    super.key,
    required this.child,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        EdgeInsets padding;
        
        if (constraints.maxWidth >= 1200.w) {
          padding = desktop ?? tablet ?? mobile ?? EdgeInsets.all(16.w);
        } else if (constraints.maxWidth >= 768.w) {
          padding = tablet ?? mobile ?? EdgeInsets.all(16.w);
        } else {
          padding = mobile ?? EdgeInsets.all(16.w);
        }

        return Padding(
          padding: padding,
          child: child,
        );
      },
    );
  }
}

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Decoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double containerMaxWidth = maxWidth ?? double.infinity;
        
        // Adjust max width based on screen size
        if (constraints.maxWidth >= 1200.w) {
          containerMaxWidth = maxWidth ?? 1200.w;
        } else if (constraints.maxWidth >= 768.w) {
          containerMaxWidth = maxWidth ?? constraints.maxWidth * 0.9;
        }

        return Center(
          child: Container(
            constraints: BoxConstraints(maxWidth: containerMaxWidth),
            padding: padding,
            margin: margin,
            decoration: decoration,
            child: child,
          ),
        );
      },
    );
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int columns;
        
        if (constraints.maxWidth >= 1200.w) {
          columns = desktopColumns;
        } else if (constraints.maxWidth >= 768.w) {
          columns = tabletColumns;
        } else {
          columns = mobileColumns;
        }

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            crossAxisSpacing: spacing.w,
            mainAxisSpacing: runSpacing.h,
            childAspectRatio: 1.0,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
